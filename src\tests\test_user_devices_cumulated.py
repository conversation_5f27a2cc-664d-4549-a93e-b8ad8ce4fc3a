from chispa.dataframe_comparer import *
from datetime import datetime

from ..jobs.user_devices_cumulated import do_user_devices_cumulated
from collections import namedtuple

events = namedtuple("events",  "user_id event_time device_id")
devices = namedtuple("devices",  "device_id browser_type")
user_devices_cumulated = namedtuple("user_devices_cumulated",  "user_id device_id browser_type date device_activity_datelist")

def test_when_no_previous_user_cumulated_data(spark):

    # Arrange
    current_date = datetime.strptime("2023-01-02, %Y-%m-%d")

    event_data = [
        events(user_id=1, event_time"2023-01-02 00:00:00", device_id=1),
        events(user_id=2, event_time"2023-01-02 00:00:00", device_id=2),
        events(user_id=3, event_time"2023-01-02 00:00:00", device_id=3),
    ]

    devices_data = [
        devices(device_id=1, browser_type="Chrome"),
        devices(device_id=2, browser_type="Firefox"),
        devices(device_id=3, browser_type="Safari"),
    ]

    
    expected_values = [
        user_device_cumulated(user_id=1, device_id=1, browser_type="Chrome", date=current_date, device_activity_datelist=[current_date]),
        user_device_cumulated(user_id=2, device_id=2, browser_type="Firefox", date=current_date, device_activity_datelist=[current_date]),
        user_device_cumulated(user_id=3, device_id=3, browser_type="Safari", date=current_date, device_activity_datelist=[current_date])
    ]

    events_df = spark.createDataFrame(events_data)
    devices_df = spark.createDataFrame(devices_data)
    user_devices_cumulated_df = spark.createDataFrame([], "user_id int, device_id int, browser_type string, date int, device_activity_datelsit array<date>")
    expected_df = spark.createDataFrame(expected_values)

    # Act
    actual_df = do_users_devices_cumulated(spark, events_df, devices_df, user_devices_cumulated_df)

    #Assert
    assert_df_equality(actual_df, expected_df)

def test_when_no_duplicate_rows(spark):

    # Arrange
    current_date = datetime.strptime("2023-01-02", "%Y-%m-%d").date()

    event_data = [
        events(user_id=1, event_time="2023-01-02 00:00:00", device_id=1),
        events(user_id=1, event_time="2023-01-02 00:00:00", device_id=1),
        events(user_id=1, event_time="2023-01-02 00:00:00", device_id=1),
    ]

    devices_data = [
        devices(device_id=1, browser_type="Chrome"),
    ]
    expected_data = [
        user_devices_cumulated(user_id=1, device_id=1, browser_type="Chrome", date=current_date, device_activity_datelist=[current_date])
    ]

    events_df = spark.createDataFrame(events_data)
    devices_df = spark.createDataFrame(devices_data)
    user_devices_cumulated_df = spark.createDataFrame([], "user_id int, device_id int, browser_type string, date int, device_activity_datelsit array<date>")
    expected_df = spark.createDataFrame(expected_values)

    # Act
    actual_df = do_users_devices_cumulated(spark, events_df, devices_df, user_devices_cumulated_df)

    #Assert
    assert_df_equality(actual_df, expected_df)

def test_when_no_new_data_for_specific_user_device_browser(spark):

    # Arrange
    previous_date = datetime.strptime("2023-01-01", "%Y-%m-%d").date()

    event_data = [
        events(user_id=1, event_time="2023-01-02 00:00:00", device_id=1),
    ]

    user_devices_cumulated_date = [
        user_devices_cumulated(user_id=1, device_id=1, browser_type="Chrome", date=previous_date, device_activity_datelist=[previous_date])
        user_devices_cumulated_date(user_id=2, device_id=2, browser_type="Firefox", date=previous_date, device_activity_datelist=[previous_date])
        user_devices_cumulated_date(user_id=3, device_id=3, browser_type="Safari", date=previous_date, device_activity_datelist=[previous_date])
    ]
     
    expected_data = [
        user_devices_cumulated(user_id=1, device_id=1, browser_type="Chrome", date=previous_date, device_activity_datelist=[previous_date])
        user_devices_cumulated(user_id=2, device_id=2, browser_type="Firefox", date=previous_date, device_activity_datelist=[previous_date])
        user_devices_cumulated(user_id=3, device_id=3, browser_type="Safari", date=previous_date, device_activity_datelist=[previous_date])
    ]

    events_df = spark.createDataFrame(events_data)
    devices_df = spark.createDataFrame(devices_data)
    user_devices_cumulated_df = spark.createDataFrame(user_devices_cumulated_data)
    expected_df = spark.createDataFrame(expected_data)

    # Act
    actual_df = do_users_devices_cumulated(spark, events_df, devices_df, user_devices_cumulated_df)

    #Assert
    assert_df_equality(actual_df, expected_df)


def test_when_new_data_for_specific_user_device_browser_and_no_previous_data(spark):

    # Arrange
    current_date = datetime.strptime("2023-01-02", "%Y-%m-%d").date()
    previous_date = datetime.strptime("2023-01-01", "%Y-%m-%d").date()

    event_data = [
        events(user_id=1, event_time="2023-01-02 00:00:00", device_id=1),
    ]
    devices_date = [
        devices(device_id=1, browser_type="Chrome"),
    ]
    user_devices_cumulated_data = [
        user_devices_cumulated(user_id=2, device_id=2, browser_type="Firefox", date=previous_date, device_activity_datelist=[previous_date]),
        user_devices_cumulated(user_id=3, device_id=3, browser_type="Safari", date=previous_date, device_activity_datelist=[previous_date])
    ]
    expected_data = [
        user_devices_cumulated(user_id=1, device_id=1, browser_type="Chrome", date=current_date, device_activity_datelist=[current_date]),
        user_devices_cumulated(user_id=2, device_id=2, browser_type="Firefox", date=previous_date, device_activity_datelist=[previous_date]),
        user_devices_cumulated(user_id=3, device_id=3, browser_type="Safari", date=previous_date, device_activity_datelist=[previous_date])
    ]

    events_df = spark.createDataFrame(events_data)
    devices_df = spark.createDataFrame(devices_data)
    user_devices_cumulated_df = spark.createDataFrame(user_devices_cumulated_data)
    expected_df = spark.createDataFrame(expected_data)

    # Act
    actual_df = do_users_devices_cumulated(spark, events_df, devices_df, user_devices_cumulated_df)

    #Assert
    assert_df_equality(actual_df, expected_df)



def test_when_user_has_multiple_devices(spark):

    #Arrange
    current_date = datetime.strptime("2023-01-02", "%Y-%m-%d").date()

    event_data = [
        events(user_id=1, event_time="2023-01-02 00:00:00", device_id=1),
        events(user_id=1, event_time="2023-01-02 00:00:00", device_id=2),
        events(user_id=1, event_time="2023-01-02 00:00:00", device_id=3),
    ]

    devices_data = [
        devices(device_id=1, browser_type="Chrome"),
        devices(device_id=2, browser_type="Firefox"),
        devices(device_id=3, browser_type="Safari"),
    ]

    expected_data = [
        user_devices_cumulated(user_id=1, device_id=1, browser_type="Chrome", date=current_date, device_activity_datelist=[current_date]),
        user_devices_cumulated(user_id=1, device_id=2, browser_type="Firefox", date=current_date, device_activity_datelist=[current_date]),
        user_devices_cumulated(user_id=1, device_id=3, browser_type="Safari", date=current_date, device_activity_datelist=[current_date]),
    ]

def test_when_user_has_multiple_browser(spark):

    #Arrange
    current_date = datetime.strptime("2023-01-02", "%Y-%m-%d").date()

    event_data = [
        events(user_id=1, event_time="2023-01-02 00:00:00", device_id=1),
        events(user_id=1, event_time="2023-01-02 00:00:00", device_id=2),
        events(user_id=1, event_time="2023-01-02 00:00:00", device_id=3),
    ]

    devices_data = [
        devices(device_id=1, browser_type="Chrome"),
        devices(device_id=2, browser_type="Chrome"),
        devices(device_id=3, browser_type="Chrome"),
    ]

    expected_data = [
        user_devices_cumulated(user_id=1, device_id=1, browser_type="Chrome", date=current_date, device_activity_datelist=[current_date]),
        user_devices_cumulated(user_id=1, device_id=2, browser_type="Chrome", date=current_date, device_activity_datelist=[current_date]),
        user_devices_cumulated(user_id=1, device_id=3, browser_type="Chrome", date=current_date, device_activity_datelist=[current_date]),
    ]

    events_df = spark.createDataFrame(events_data)
    devices_df = spark.createDataFrame(devices_data)
    user_devices_cumulated_df = spark.createDataFrame([], "user_id int, device_id int, browser_type string, date int, device_activity_datelsit array<date>")
    expected_df = spark.createDataFrame(expected_values)

    # Act
    actual_df = do_users_devices_cumulated(spark, events_df, devices_df, user_devices_cumulated_df)

    #Assert
    assert_df_equality(actual_df, expected_df)

def test_when_device_across_multiple_users(spark):

    #Arrange
    current_date = datetime.strptime("2023-01-02", "%Y-%m-%d").date()

    event_data = [
        events(user_id=1, event_time="2023-01-02 00:00:00", device_id=1),
        events(user_id=2, event_time="2023-01-02 00:00:00", device_id=1),
        events(user_id=3, event_time="2023-01-02 00:00:00", device_id=1),
    ]

    devices_data = [
        devices(device_id=1, browser_type="Chrome"),
    ]

    expected_data = [
        user_devices_cumulated(user_id=1, device_id=1, browser_type="Chrome", date=current_date, device_activity_datelist=[current_date]),
        user_devices_cumulated(user_id=2, device_id=1, browser_type="Chrome", date=current_date, device_activity_datelist=[current_date]),
        user_devices_cumulated(user_id=3, device_id=1, browser_type="Chrome", date=current_date, device_activity_datelist=[current_date]),
    ]

    events_df = spark.createDataFrame(events_data)
    devices_df = spark.createDataFrame(devices_data)
    user_devices_cumulated_df = spark.createDataFrame([], "user_id int, device_id int, browser_type string, date int, device_activity_datelsit array<date>")
    expected_df = spark.createDataFrame(expected_values)

    # Act
    actual_df = do_users_devices_cumulated(spark, events_df, devices_df, user_devices_cumulated_df)

    #Assert
    assert_df_equality(actual_df, expected_df)



def test_when_multiple_dates_in_events_only_current_date(spark):

    #Arrange
    current_date = datetime.strptime("2023-01-02", "%Y-%m-%d").date()

    event_data = [
        events(user_id=1, event_time="2023-01-02 00:00:00", device_id=1),
        events(user_id=1, event_time="2023-01-02 00:00:00", device_id=1),
        events(user_id=1, event_time="2023-01-02 00:00:00", device_id=1),
    ]

    devices_data = [
        devices(device_id=1, browser_type="Chrome"),
    ]

    expected_data = [
        user_devices_cumulated(user_id=1, device_id=1, browser_type="Chrome", date=current_date, device_activity_datelist=[current_date]),
    ]

    events_df = spark.createDataFrame(events_data)
    devices_df = spark.createDataFrame(devices_data)
    user_devices_cumulated_df = spark.createDataFrame([], "user_id int, device_id int, browser_type string, date int, device_activity_datelsit array<date>")
    expected_df = spark.createDataFrame(expected_values)

    # Act
    actual_df = do_users_devices_cumulated(spark, events_df, devices_df, user_devices_cumulated_df)

    #Assert
    assert_df_equality(actual_df, expected_df)
    