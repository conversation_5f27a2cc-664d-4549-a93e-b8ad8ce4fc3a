from pyspark.sql import SparkSession

def do_actors_history_scd(spark, actors_df, actors_films_df, actors_history_df):
    query = f"""
        with last_year as (
		select * from actors
		where current_year = 2000
),  this_year as (
	select * from actors_films
	where year = 2001
), rating as (
	select actor_id, actor, year,
	array_agg(
		rwo(
			film,
			year,
			votes,
			rating,
			film_id
		)::films
		) as current_films,
		avg(rating) as avg_rating
		from this_year
		group by actor_id, actor, year

select
	coalesce (ly.actor_id, ty.actor_id) as actor_id,
	coalesce (ly.actor, ty.actor) as actor,
	coalesce (ly.current_year + 1, ty.year) as current_year,
	case
		when ty is null then ly.films
		when ly is null then array(struct("film", ty.film, "votes", ty.votes, "rating", ty.rating, "film_id", ty.film_id))
		else concat(ly.films, array(struct("film", ty.film, "votes", ty.votes, "rating", ty.rating, "film_id", ty.film_id))
	end as films,
	case 
		when r.rating is not null
		then 
			case
				when r.avg_rating >= 8 then 'star'
				when r.avg_rating >= 7 then 'good'
				when r.avg_rating >= 6 then 'average'
				else 'bad'
			end::quality_class
		else ly.quality_class
	end as quality_class,
	case
		when r.year is null
		then false
		else true
	end as is_active
	from rating as r
	full outer join last_year as ly
	on ty.actor_id = ly.actor_id;
    """
    actors_df.createOrReplaceTempView("actors")
	actors_films_df.createOrReplaceTempView("actors_films")
	actors_history_df.createOrReplaceTempView("actors_history_scd")
    return spark.sql(query)

def main():
	ds = "2023-01-01"
    spark = SparkSession.builder \
      .master("local") \
      .appName("actors_history_scd") \
      .getOrCreate()


    output_df = do_actors_history_scd(spark, spark.table("actors"), spark.table("actors_films"), spark.table("actors_history_scd"))
    output_df.write.mode("overwrite").insertInto("actors_history_scd")
    
