@echo off
echo ========================================
echo  Docker Network Issues Troubleshooter
echo ========================================

echo.
echo 🔧 This script helps fix common Docker network issues
echo    that cause TLS handshake timeouts and download failures.

echo.
echo 🛑 Step 1: Stopping all containers...
docker-compose down
docker stop $(docker ps -aq) 2>nul

echo.
echo 🧹 Step 2: Cleaning up Docker system...
echo    This removes unused images and networks to free up space
docker system prune -f

echo.
echo 🔄 Step 3: Restarting Docker daemon...
echo    Please restart Docker Desktop manually:
echo    1. Right-click Docker Desktop in system tray
echo    2. Select "Restart Docker Desktop"
echo    3. Wait for Docker to fully restart (green icon)
echo    4. Press any key here to continue
pause

echo.
echo 🐳 Step 4: Verifying Docker is running...
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not running! Please start Docker Desktop and try again.
    pause
    exit /b 1
)
echo ✅ Docker is running

echo.
echo 🌐 Step 5: Testing network connectivity...
echo    Testing connection to Docker Hub...
ping -n 1 registry-1.docker.io >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Network connectivity to Docker Hub is working
) else (
    echo ⚠️  Network connectivity issues detected
    echo    Please check your internet connection and firewall settings
)

echo.
echo 📥 Step 6: Trying to pull images with increased timeout...
echo    Setting Docker client timeout to 10 minutes...

REM Try pulling with explicit timeout settings
echo.
echo 🔄 Attempting to pull Spark-Iceberg image...
docker pull --platform linux/amd64 tabulario/spark-iceberg:1.5.0
if %errorlevel% neq 0 (
    echo ❌ Failed to pull Spark image
    echo    Try the following:
    echo    1. Check your internet connection
    echo    2. Try again later (Docker Hub may be busy)
    echo    3. Consider using a VPN if in a restricted network
    echo    4. Check Docker Desktop settings for proxy configuration
    goto end_script
)
echo ✅ Spark-Iceberg image pulled successfully

echo.
echo 🔄 Attempting to pull REST Catalog image...
docker pull --platform linux/amd64 tabulario/iceberg-rest:1.5.0
if %errorlevel% neq 0 (
    echo ❌ Failed to pull REST image
    goto end_script
)
echo ✅ REST Catalog image pulled successfully

echo.
echo 🚀 Step 7: Starting services...
docker-compose up -d

if %errorlevel% equ 0 (
    echo ✅ Services started successfully!
    echo.
    echo 📊 Access your setup at:
    echo   • Jupyter: http://localhost:8888
    echo   • Spark UI: http://localhost:4040
    echo.
    echo 🎉 Network issues resolved!
) else (
    echo ❌ Services failed to start
    echo    Check logs with: docker-compose logs
)

:end_script
echo.
echo ========================================
echo  Troubleshooting Complete
echo ========================================
echo.
echo 💡 Additional Tips:
echo   • If issues persist, try using mobile hotspot temporarily
echo   • Check Windows Defender/Antivirus firewall settings
echo   • Ensure Docker Desktop has sufficient memory allocated (4GB+)
echo   • Try running PowerShell/Command Prompt as Administrator
echo.
echo Press any key to exit...
pause >nul
