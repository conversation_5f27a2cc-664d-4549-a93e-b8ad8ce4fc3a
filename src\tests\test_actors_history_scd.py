from chispa.dataframe_comparer import *
from datetime import datetime

from ..jobs.actors_history_scd import do_actors_history_scd
from collections import namedtuple

actors = namedtuple("actors", "actor_id actor current_year films")
actors_films = namedtuple("actors_films", "actor_id actor film votes rating film_id year")
actors_history_scd = namedtuple("actors_history_scd", "actor_id actor current_year films quality_class_is_active")
films = namedtuple("films", "film votes rating film_id")

def test_when_only_new_actors(spark):

    #Arrange
    actor_films_data = [
        actors_films(actor_id=1, "film 1", votes=100, rating=8.0, film_id=1, year=2001),
        actors_films(actor_id=2, "film 2", votes=200, rating=9.0, film_id=2, year=2001)
    ]
    expected_values = [
        actors_history_scd(actor_id=1, actor="Actor 1", current_year = 2001, films=[films(film="film 1", votes=100, rating=8.0, film_id=1)]),
        actors_history_scd(actor_id=2, actor="Actor 2", current_year = 2001, films=[films(film="film 2", votes=200, rating=9.0, film_id=2)])
    ]

    actors_df = spark.createDataFrame([], "actor_id int, actor string, current_year int, films array<struct<film:string, votes:int, rating:double, film_id:int>>")

    actors_films_df = spark.createDataFrame(actor_films_data , "actor_id int, actor string, film string, votes int, rating double, film_id int, year int")
    actors_history_scd_df = spark.createDataFrame([], "actor_id int, actor string, current_year int, films array<struct<film:string, votes:int, rating:double, film_id:int>>, quality_class string, is_active boolean")
    expected_df = spark.createDataFrame(expected_values , "actor_id int, actor string, current_year int, films array<struct<film:string, votes:int, rating:double, film_id:int>>, quality_class string, is_active boolean")

    #Act
    actual_df = do_actors_history_scd(spark, actors_df, actors_films_df, actors_history_scd_df)
 
    #Assert
    assert_df_equality(actual_df, expected_df)

def test_when_some_new_and_some_previous(spark):

    #arrange
    actors_data = [
        actors(actor_id=1, actor="Actor 1", current_year=2000, films=[films(film="film 1", votes=100, rating=8.0, film_id=1)]),
        actors(actor_id=2, actor="Actor 2", current_year=2000, films=[films(film="film 2", votes=200, rating=9.0, film_id=2)])
    ]
    actor_films_data = [
        actors_films(actor_id=2, "Actor 2", "film 3", votes=300, rating=7.0, film_id=3, year=2001),
        actors_films(actor_id=3, "Actor 3", "film 4", votes=400, rating=6.0, film_id=4, year=2001)
    ]

    expected_values = [
        actors_history_scd(actor_id=1, actor="Actor 1", current_year = 2001, films=[films(film="film 1", votes=100, rating=8.0, film_id=1)]),
        actors_history_scd(actor_id=2, actor="Actor 2", current_year = 2001, films=[films(film="film 2", votes=200, rating=9.0, film_id=2), films(film="film 3", votes=300, rating=7.0, film_id=3)]),
        actors_history_scd(actor_id=3, actor="Actor 3", current_year = 2001, films=[films(film="film 4", votes=400, rating=6.0, film_id=4)])
    ]

    actors_df = spark.createDataFrame(actors_data, "actor_id int, actor string, current_year int, films array<struct<film:string, votes:int, rating:double, film_id:int>>")
