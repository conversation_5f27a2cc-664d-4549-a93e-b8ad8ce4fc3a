@echo off
echo ========================================
echo  Local Python Spark Setup (No Docker)
echo  For Slow Internet Connections
echo ========================================

echo.
echo 🐍 This setup uses local Python instead of Docker
echo    Much faster setup with minimal downloads!

REM Check if Python is installed
echo.
echo 🔍 Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python is not installed or not in PATH
    echo    Please install Python 3.8+ from https://python.org
    echo    Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)

python --version
echo ✅ Python is available

REM Check if Java is installed (required for Spark)
echo.
echo ☕ Checking Java installation...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java is not installed or not in PATH
    echo    Please install Java 11 or 17 from:
    echo    https://adoptium.net/temurin/releases/
    pause
    exit /b 1
)

java -version
echo ✅ Java is available

REM Create virtual environment
echo.
echo 📦 Creating Python virtual environment...
if exist "venv" (
    echo    Virtual environment already exists, using existing one
) else (
    python -m venv venv
    echo ✅ Virtual environment created
)

REM Activate virtual environment
echo.
echo 🔄 Activating virtual environment...
call venv\Scripts\activate.bat

REM Install minimal requirements
echo.
echo 📥 Installing Python packages (small downloads)...
echo    This will install: pyspark, jupyter, pandas, pyarrow

pip install --upgrade pip
pip install pyspark==3.5.0
pip install jupyter
pip install pandas
pip install pyarrow
pip install requests

echo ✅ Python packages installed

REM Create directories
echo.
echo 📁 Creating directories...
if not exist "warehouse" mkdir warehouse
if not exist "notebooks" mkdir notebooks
if not exist "data" mkdir data
if not exist "src" mkdir src
echo ✅ Directories created

REM Create local Spark configuration
echo.
echo ⚙️  Creating local Spark configuration...
(
echo import os
echo import sys
echo from pyspark.sql import SparkSession
echo from pyspark.sql.functions import *
echo.
echo def create_local_spark^(app_name="LocalSpark"^):
echo     """Create a local Spark session optimized for 6GB RAM"""
echo     
echo     # Set up Spark configuration for local mode
echo     spark = SparkSession.builder \
echo         .appName^(app_name^) \
echo         .master^("local[2]"^) \
echo         .config^("spark.driver.memory", "1g"^) \
echo         .config^("spark.executor.memory", "1g"^) \
echo         .config^("spark.sql.adaptive.enabled", "true"^) \
echo         .config^("spark.sql.adaptive.coalescePartitions.enabled", "true"^) \
echo         .config^("spark.serializer", "org.apache.spark.serializer.KryoSerializer"^) \
echo         .getOrCreate^(^)
echo     
echo     print^("✅ Local Spark session created successfully"^)
echo     print^(f"   Spark Version: {spark.version}"^)
echo     print^(f"   Master: {spark.sparkContext.master}"^)
echo     print^(f"   App Name: {spark.sparkContext.appName}"^)
echo     
echo     return spark
echo.
echo if __name__ == "__main__":
echo     spark = create_local_spark^(^)
echo     
echo     # Test basic functionality
echo     test_data = [^(1, "Alice"^), ^(2, "Bob"^), ^(3, "Charlie"^)]
echo     df = spark.createDataFrame^(test_data, ["id", "name"]^)
echo     print^("\\n📊 Test DataFrame:"^)
echo     df.show^(^)
echo     
echo     print^("\\n🎉 Local Spark setup is working!"^)
echo     spark.stop^(^)
) > src\local_spark_setup.py

echo ✅ Local Spark configuration created

REM Create simple demo notebook
echo.
echo 📓 Creating demo notebook...
(
echo {
echo  "cells": [
echo   {
echo    "cell_type": "markdown",
echo    "metadata": {},
echo    "source": [
echo     "# Local Spark Demo\\n",
echo     "## No Docker Required - Perfect for Slow Internet\\n",
echo     "\\n",
echo     "This notebook demonstrates Spark running locally without Docker."
echo    ]
echo   },
echo   {
echo    "cell_type": "code",
echo    "execution_count": null,
echo    "metadata": {},
echo    "outputs": [],
echo    "source": [
echo     "import sys\\n",
echo     "sys.path.append^('../src'^)\\n",
echo     "from local_spark_setup import create_local_spark\\n",
echo     "\\n",
echo     "# Create local Spark session\\n",
echo     "spark = create_local_spark^('LocalDemo'^)"
echo    ]
echo   },
echo   {
echo    "cell_type": "code",
echo    "execution_count": null,
echo    "metadata": {},
echo    "outputs": [],
echo    "source": [
echo     "# Create sample data\\n",
echo     "data = [^(1, 'Alice', 25^), ^(2, 'Bob', 30^), ^(3, 'Charlie', 35^)]\\n",
echo     "df = spark.createDataFrame^(data, ['id', 'name', 'age']^)\\n",
echo     "\\n",
echo     "print^('Sample Data:'^)\\n",
echo     "df.show^(^)"
echo    ]
echo   },
echo   {
echo    "cell_type": "code",
echo    "execution_count": null,
echo    "metadata": {},
echo    "outputs": [],
echo    "source": [
echo     "# Perform some analytics\\n",
echo     "print^('Analytics:'^)\\n",
echo     "df.groupBy^(^).avg^('age'^).show^(^)\\n",
echo     "df.filter^(df.age ^> 25^).show^(^)"
echo    ]
echo   }
echo  ],
echo  "metadata": {
echo   "kernelspec": {
echo    "display_name": "Python 3",
echo    "language": "python",
echo    "name": "python3"
echo   }
echo  },
echo  "nbformat": 4,
echo  "nbformat_minor": 4
echo }
) > notebooks\local_spark_demo.ipynb

echo ✅ Demo notebook created

REM Start Jupyter
echo.
echo 🚀 Starting Jupyter Notebook...
echo    Jupyter will open in your browser automatically
echo    Use Ctrl+C to stop Jupyter when done

cd notebooks
jupyter notebook --ip=0.0.0.0 --port=8888 --no-browser --allow-root

echo.
echo 🎉 Setup complete! 
echo    Your local Spark environment is ready to use.
pause
