from pyspark.sql import SparkSession

def do_user_devices_datelist_int(spark, user_devices_cumulated_df):
    query = f"""
        with user_devices as (
          select *
          from user_devices_cumulated
          where date = date ('2023-01-01')
        ), series as (
          select date_add("2023-01-01", cast(id as int)) as _series_date from date_series
        ), place_holder_ints as (
          select
            case
              when array_contains(device_activity_datelist, cast(series_date as date))
              then cast(pow(2, 32 - datediff(date - date(series_date))) as bigint)
              else 0
            end as placeholder_int_value, *
          from user_devices as ud
          cross join series as s
        )
        select
          user_id,
          device_id,
          browser_type,
          device_activity_datelist,
          cast(sum(placeholder_int_value) as bigint) as datelist_int
        from place_holder_int as p
        group by user_id, device_id, browser_type, device_activity_datelist;
    """
    spark.range(0, 31).createOrReplaceTempView("date_series")
    user_devices_cumulated_df.createOrReplaceTempView("user_devices_cumulated")

    return spark.sql(query)

def main():
    spark = SparkSession.builder \
      .master("local") \
      .appName("user_devices_datelist_int") \
      .getOrCreate()

    spark.range(0, 31).createOrReplaceTempView("date_series")
    spark.sql("select date_add("2023-01-01", cast(id as int)) as _series_date from date_series").show()

    output_df = do_user_devices_datelist_int(spark, spark.table("user_devices_cumulated"))
    output_df.write.mode("overwrite").insertInto("user_devices_datelist_int")
    
