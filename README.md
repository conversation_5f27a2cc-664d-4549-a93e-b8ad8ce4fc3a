# Spark-Iceberg Local Setup for Windows 10 (6GB RAM)

This setup has been optimized for Windows 10 machines with 6GB RAM using Docker, with all cloud services removed and replaced with local filesystem storage.

## 🚀 Quick Start

### Prerequisites
- Windows 10 with Docker Desktop installed and running
- At least 6GB RAM (3GB will be used by Docker containers)
- Docker Desktop with at least 4GB memory allocated

### Deployment Options

#### Option 1: Robust Deployment (Recommended for Network Issues)
```batch
# Run the robust deployment script with network retry logic
deploy_robust.bat
```

#### Option 2: Standard Deployment
```batch
# Run the standard deployment script
deploy.bat
```

#### Option 3: Manual Deployment
```batch
# Start the containers manually
docker-compose up -d

# Wait for services to start (30-60 seconds)
# Then access Jupyter at http://localhost:8888
```

#### Option 4: Network Troubleshooting
If you encounter TLS handshake timeouts or download failures:
```batch
# Run the network troubleshooter
fix_network_issues.bat
```

## 📊 Access Points

Once deployed, you can access:
- **Jupyter Notebook**: http://localhost:8888
- **Spark UI**: http://localhost:4040 (when Spark jobs are running)
- **Spark Master UI**: http://localhost:8080
- **REST Catalog**: http://localhost:8181

## 📝 Available Notebooks

1. **`local_iceberg_demo.ipynb`** - Complete demo of local Iceberg setup
2. **`event_data_pyspark.ipynb`** - Updated original notebook for local filesystem
3. **Other existing notebooks** - May need minor updates for local setup

## 🔧 Configuration Details

### Memory Optimization for 6GB RAM
- **Spark Driver Memory**: 1GB
- **Spark Executor Memory**: 1GB
- **Container Memory Limits**: 2.5GB for Spark, 512MB for REST
- **Total Docker Memory Usage**: ~3GB

### Storage Configuration
- **Warehouse Location**: `./warehouse` (local filesystem)
- **Data Location**: `./data`
- **Notebooks**: `./notebooks`
- **No cloud storage dependencies** (MinIO/S3 removed)

### Key Changes from Original Setup
- ✅ Removed MinIO S3 storage
- ✅ Configured Iceberg for local filesystem
- ✅ Optimized memory settings for 6GB RAM
- ✅ Added resource limits to containers
- ✅ Created initialization scripts for easy setup
- ✅ Updated notebooks for local configuration

## 🧪 Testing Your Setup

### Automated Testing
```python
# Inside the container, run:
python test_setup.py
```

### Manual Testing
1. Open http://localhost:8888
2. Run the `local_iceberg_demo.ipynb` notebook
3. Verify all cells execute successfully

## 🛠️ Troubleshooting

### Common Issues

**TLS handshake timeout / Network errors:**
- Run `fix_network_issues.bat` to resolve network problems
- Try `deploy_robust.bat` which includes retry logic
- Check internet connection and firewall settings
- Consider using mobile hotspot temporarily
- Restart Docker Desktop completely

**Docker containers won't start:**
- Ensure Docker Desktop is running
- Check available memory (need at least 4GB for Docker)
- Try: `docker-compose down` then `docker-compose up -d`
- Run as Administrator if permission issues occur

**Out of memory errors:**
- Reduce Spark memory settings in `spark-defaults.conf`
- Close other applications to free up RAM
- Consider increasing Docker memory allocation
- Use smaller datasets for testing

**Jupyter notebook not accessible:**
- Wait 30-60 seconds after starting containers
- Check container status: `docker-compose ps`
- Check logs: `docker-compose logs spark-iceberg`
- Try accessing http://localhost:8888 directly

### Performance Tips for 6GB RAM
- Close unnecessary applications before starting
- Use smaller datasets for testing
- Consider processing data in smaller batches
- Monitor memory usage with Task Manager

## 📁 File Structure
```
my_setup/
├── docker-compose.yaml          # Optimized container configuration
├── spark-defaults.conf          # Spark memory optimization
├── spark_iceberg_init.py        # Initialization helper
├── deploy.bat                   # Windows deployment script
├── test_setup.py               # Setup validation script
├── notebooks/
│   ├── local_iceberg_demo.ipynb # New demo notebook
│   └── event_data_pyspark.ipynb # Updated original notebook
├── data/                       # Your CSV data files
├── warehouse/                  # Iceberg tables (local filesystem)
└── src/                       # Source code
```

## 🎯 Next Steps

1. **Start with the demo**: Run `local_iceberg_demo.ipynb` to see all features
2. **Explore your data**: Use `event_data_pyspark.ipynb` with your CSV files
3. **Create new tables**: Use the local catalog (`local.database.table`)
4. **Monitor performance**: Keep an eye on memory usage

## 💡 Development Tips

- Use `local.database.table` format for Iceberg tables
- Tables are stored in `./warehouse` directory
- Restart containers if you encounter memory issues
- Use the initialization script for consistent Spark configuration