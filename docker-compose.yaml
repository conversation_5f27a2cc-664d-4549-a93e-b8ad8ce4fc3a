services:
  spark-iceberg:
    image: tabulario/spark-iceberg
    container_name: spark-iceberg
    networks:
      iceberg_net:
    depends_on:
      - rest
    volumes:
      - ./warehouse:/home/<USER>/warehouse
      - ./notebooks:/home/<USER>/notebooks/notebooks
      - ./data:/home/<USER>/data
      - ./src:/home/<USER>/src
    environment:
      # Spark Configuration for 6GB RAM system
      - SPARK_DRIVER_MEMORY=1g
      - SPARK_EXECUTOR_MEMORY=1g
      - SPARK_DRIVER_MAX_RESULT_SIZE=512m
      - SPARK_SQL_ADAPTIVE_ENABLED=true
      - SPARK_SQL_ADAPTIVE_COALESCE_PARTITIONS_ENABLED=true
      - SPARK_SERIALIZER=org.apache.spark.serializer.KryoSerializer
      # JVM Settings for low memory
      - SPARK_DRIVER_JAVA_OPTIONS=-Xms512m -Xmx1g -XX:+UseG1GC -XX:MaxGCPauseMillis=200
      - SPARK_EXECUTOR_JAVA_OPTIONS=-Xms512m -Xmx1g -XX:+UseG1GC -XX:MaxGCPauseMillis=200
    ports:
      - 8888:8888
      - 8080:8080
      - 10000:10000
      - 10001:10001
    # Resource limits for 6GB RAM system
    deploy:
      resources:
        limits:
          memory: 2.5G
        reservations:
          memory: 1.5G
    restart: unless-stopped

  rest:
    image: tabulario/iceberg-rest
    container_name: iceberg-rest
    networks:
      iceberg_net:
    ports:
      - 8181:8181
    environment:
      # Local filesystem configuration instead of S3
      - CATALOG_WAREHOUSE=file:///home/<USER>/warehouse
      - CATALOG_IO__IMPL=org.apache.iceberg.hadoop.HadoopFileIO
      - CATALOG_URI=http://rest:8181
    volumes:
      - ./warehouse:/home/<USER>/warehouse
    # Resource limits for REST service
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    restart: unless-stopped

networks:
  iceberg_net:
    driver: bridge