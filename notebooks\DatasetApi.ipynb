{"cells": [{"cell_type": "code", "execution_count": 1, "id": "22b842be-6a82-4127-b937-ead4103a92e8", "metadata": {}, "outputs": [{"data": {"text/plain": ["Intitializing Scala interpreter ..."]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["Spark Web UI available at http://5a3580fc8c49:4041\n", "SparkContext available as 'sc' (version = 3.5.1, master = local[*], app id = local-1734347929564)\n", "SparkSession available as 'spark'\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["res0: org.apache.spark.sql.SparkSession = org.apache.spark.sql.SparkSession@4f49c708\n"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["spark"]}, {"cell_type": "code", "execution_count": 32, "id": "73b5384f-be28-49e3-9bcf-4b9783ba7d91", "metadata": {}, "outputs": [{"ename": "org.apache.spark.SparkRuntimeException", "evalue": " Error while decoding: java.lang.NullPointerException", "output_type": "error", "traceback": ["org.apache.spark.SparkRuntimeException: Error while decoding: java.lang.NullPointerException", "newInstance(class Event).", "  at org.apache.spark.sql.errors.QueryExecutionErrors$.expressionDecodingError(QueryExecutionErrors.scala:1543)", "  at org.apache.spark.sql.catalyst.encoders.ExpressionEncoder$Deserializer.apply(ExpressionEncoder.scala:178)", "  at org.apache.spark.sql.catalyst.encoders.ExpressionEncoder$Deserializer.apply(ExpressionEncoder.scala:166)", "  at scala.collection.TraversableLike.$anonfun$map$1(TraversableLike.scala:286)", "  at scala.collection.IndexedSeqOptimized.foreach(IndexedSeqOptimized.scala:36)", "  at scala.collection.IndexedSeqOptimized.foreach$(IndexedSeqOptimized.scala:33)", "  at scala.collection.mutable.ArrayOps$ofRef.foreach(ArrayOps.scala:198)", "  at scala.collection.TraversableLike.map(TraversableLike.scala:286)", "  at scala.collection.TraversableLike.map$(TraversableLike.scala:279)", "  at scala.collection.mutable.ArrayOps$ofRef.map(ArrayOps.scala:198)", "  at org.apache.spark.sql.Dataset.collectFromPlan(Dataset.scala:4177)", "  at org.apache.spark.sql.Dataset.$anonfun$head$1(Dataset.scala:3161)", "  at org.apache.spark.sql.Dataset.$anonfun$withAction$2(Dataset.scala:4167)", "  at org.apache.spark.sql.execution.QueryExecution$.withInternalError(QueryExecution.scala:526)", "  at org.apache.spark.sql.Dataset.$anonfun$withAction$1(Dataset.scala:4165)", "  at org.apache.spark.sql.execution.SQLExecution$.$anonfun$withNewExecutionId$6(SQLExecution.scala:118)", "  at org.apache.spark.sql.execution.SQLExecution$.withSQLConfPropagated(SQLExecution.scala:195)", "  at org.apache.spark.sql.execution.SQLExecution$.$anonfun$withNewExecutionId$1(SQLExecution.scala:103)", "  at org.apache.spark.sql.SparkSession.withActive(SparkSession.scala:827)", "  at org.apache.spark.sql.execution.SQLExecution$.withNewExecutionId(SQLExecution.scala:65)", "  at org.apache.spark.sql.Dataset.withAction(Dataset.scala:4165)", "  at org.apache.spark.sql.Dataset.head(Dataset.scala:3161)", "  at org.apache.spark.sql.Dataset.take(Dataset.scala:3382)", "  ... 49 elided", "Caused by: java.lang.NullPointerException", ""]}], "source": ["import org.apache.spark.sql.SparkSession \n", "\n", "val sparkSession = SparkSession.builder.appName(\"Juptyer\").getOrCreate()\n", "\n", "//TODO Illustrate how this fails if you change from Option[String] to String for referrer\n", "case class Event (\n", "   //Option is a way to handle NULL more gracefully\n", "    user_id: Option[Integer],\n", "    device_id: Option[Integer],\n", "    referrer: Option[String],\n", "    host: <PERSON>,\n", "    url: String,\n", "    event_time: String\n", ")\n", "\n", "\n", "dummyData = List(\n", "        Event(user_id=1, device_id=2, referrer=\"linkedin\", host=\"eczachly.com\", url=\"/signup\", event_time=\"2023-01-01\"),\n", "        Event(user_id=3, device_id=7, referrer=\"twitter\", host=\"eczachly.com\", url=\"/signup\", event_time=\"2023-01-01\")\n", "    )\n", "\n", "//TODO Illustrate how this fails if you change from Option[Long] to Long\n", "case class Device (\n", "    device_id: Integer,\n", "    browser_type: String,\n", "    os_type: String,\n", "    device_type: String\n", ")\n", "\n", "case class EventWithDeviceInfo (\n", "   user_id: <PERSON><PERSON><PERSON>,\n", "    device_id: Integer,\n", "    browser_type: String,\n", "    os_type: String,\n", "    device_type: String,\n", "    referrer: String,\n", "    host: <PERSON>,\n", "    url: String,\n", "    event_time: String\n", ")\n", "\n", "// When should you use each type?\n", "import sparkSession.implicits._\n", "\n", "// Applying this case class before hand is very powerful, enforces Nullability/non-nullability at runtime!\n", "val events: Dataset[Event] = sparkSession.read.option(\"header\", \"true\")\n", "                        .option(\"inferSchema\", \"true\")\n", "                        .csv(\"/home/<USER>/data/events.csv\")\n", "                        .as[Event]\n", "\n", "val devices: Dataset[Device] = sparkSession.read.option(\"header\", \"true\")\n", "                        .option(\"inferSchema\", \"true\")\n", "                        .csv(\"/home/<USER>/data/devices.csv\")\n", "                        .as[<PERSON><PERSON>]\n", "\n", "devices.createOrReplaceTempView(\"devices\")\n", "events.createOrReplaceTempView(\"events\")\n", "\n", "// For simple transformations, you can see that these approaches are very similar. Dataset is winning slightly because of the quality enforcement\n", "val filteredViaDataset = events.filter(event => event.user_id.isDefined && event.device_id.isDefined)\n", "val filteredViaDataFrame = events.toDF().where($\"user_id\".isNotNull && $\"device_id\".isNotNull)\n", "val filteredViaSparkSql = sparkSession.sql(\"SELECT * FROM events WHERE user_id IS NOT NULL AND device_id IS NOT NULL\")\n", "\n", "\n", "// This will fail if user_id is None\n", "val combinedViaDatasets = filteredViaDataset\n", "    .joinWith(devices, events(\"device_id\") === devices(\"device_id\"), \"inner\")\n", "    .map{ case (event: Event, device: Device) => EventWithDeviceInfo(\n", "                  user_id=event.user_id.get,\n", "                  device_id=device.device_id,\n", "                  browser_type=device.browser_type,\n", "                  os_type=device.os_type,\n", "                  device_type=device.device_type,\n", "                  referrer=event.referrer,\n", "                  host=event.host,\n", "                  url=event.url,\n", "                  event_time=event.event_time\n", "              ) }\n", "    .map( case (row: EventWithDeviceInfo) => {\n", "        row.browser_type = toUpperCase(row.browser_type)\n", "        return row\n", "    })\n", "\n", "\n", "\n", "\n", "// DataFrames give up some of the intellisense because you no longer have static typing\n", "val combinedViaDataFrames = filteredViaDataFrame.as(\"e\")\n", "            //Make sure to use triple equals when using data frames\n", "            .join(devices.as(\"d\"), $\"e.device_id\" === $\"d.device_id\", \"inner\")\n", "            .select(\n", "              $\"e.user_id\",\n", "              $\"d.device_id\",\n", "              $\"d.browser_type\",\n", "              $\"d.os_type\",\n", "              $\"d.device_type\",\n", "              $\"e.referrer\",\n", "              $\"e.host\",\n", "              $\"e.url\",\n", "              $\"e.event_time\"\n", "            )\n", "\n", "//Creating temp views is a good strategy if you're leveraging SparkSQL\n", "filteredViaSparkSql.createOrReplaceTempView(\"filtered_events\")\n", "val combinedViaSparkSQL = spark.sql(f\"\"\"\n", "    SELECT \n", "        fe.user_id,\n", "        d.device_id,\n", "        d.browser_type,\n", "        d.os_type,\n", "        d.device_type,\n", "        fe. referrer,\n", "        fe.host,\n", "        fe.url,\n", "        fe.event_time\n", "    FROM filtered_events fe \n", "    JOIN devices d ON fe.device_id = d.device_id\n", "\"\"\")\n", "\n", "combinedViaDatasets.take(5)\n"]}, {"cell_type": "code", "execution_count": null, "id": "dd4b33aa-98c4-4f8d-8d38-f5674eb4d8ed", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "203ac70d-b0e5-474b-8c82-b1bae2624d51", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "spylon-kernel", "language": "scala", "name": "spylon-kernel"}, "language_info": {"codemirror_mode": "text/x-scala", "file_extension": ".scala", "help_links": [{"text": "MetaKernel Magics", "url": "https://metakernel.readthedocs.io/en/latest/source/README.html"}], "mimetype": "text/x-scala", "name": "scala", "pygments_lexer": "scala", "version": "0.4.1"}}, "nbformat": 4, "nbformat_minor": 5}