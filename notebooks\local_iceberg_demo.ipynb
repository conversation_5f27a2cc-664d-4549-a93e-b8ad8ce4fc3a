{"cells": [{"cell_type": "markdown", "id": "intro", "metadata": {}, "source": ["# Local Spark-Iceberg Demo\n", "## Optimized for 6GB RAM Windows 10 with Docker\n", "\n", "This notebook demonstrates how to use Spark with Iceberg on a local filesystem setup, optimized for low-memory environments."]}, {"cell_type": "code", "execution_count": null, "id": "setup", "metadata": {}, "outputs": [], "source": ["# Initialize Spark with optimized settings\n", "import sys\n", "sys.path.append('/home/<USER>')\n", "from spark_iceberg_init import initialize_spark_iceberg\n", "from pyspark.sql.functions import col, expr, current_timestamp\n", "from pyspark.sql.types import StructType, StructField, StringType, IntegerType, TimestampType\n", "\n", "# Create optimized Spark session\n", "spark = initialize_spark_iceberg(app_name=\"LocalIcebergDemo\")"]}, {"cell_type": "code", "execution_count": null, "id": "create_database", "metadata": {}, "outputs": [], "source": ["# Create and use local database\n", "print(\"🗄️  Setting up local database...\")\n", "spark.sql(\"CREATE DATABASE IF NOT EXISTS local.demo\")\n", "spark.sql(\"USE local.demo\")\n", "print(\"✅ Using database: local.demo\")\n", "\n", "# Show available databases\n", "print(\"\\n📋 Available databases:\")\n", "spark.sql(\"SHOW DATABASES\").show()"]}, {"cell_type": "code", "execution_count": null, "id": "create_sample_data", "metadata": {}, "outputs": [], "source": ["# Create sample data for demonstration\n", "print(\"📊 Creating sample data...\")\n", "\n", "sample_data = [\n", "    (1, \"<PERSON>\", \"Engineering\", 75000, \"2023-01-15\"),\n", "    (2, \"<PERSON>\", \"<PERSON>\", 65000, \"2023-02-20\"),\n", "    (3, \"<PERSON>\", \"Engineering\", 80000, \"2023-01-10\"),\n", "    (4, \"<PERSON>\", \"<PERSON>\", 70000, \"2023-03-05\"),\n", "    (5, \"<PERSON>\", \"Engineering\", 85000, \"2023-01-25\")\n", "]\n", "\n", "schema = StructType([\n", "    StructField(\"id\", IntegerType(), True),\n", "    StructField(\"name\", StringType(), True),\n", "    StructField(\"department\", StringType(), True),\n", "    StructField(\"salary\", IntegerType(), True),\n", "    StructField(\"hire_date\", StringType(), True)\n", "])\n", "\n", "df = spark.createDataFrame(sample_data, schema)\n", "df = df.withColumn(\"hire_date\", col(\"hire_date\").cast(\"date\"))\n", "df = df.withColumn(\"created_at\", current_timestamp())\n", "\n", "print(f\"✅ Created DataFrame with {df.count()} records\")\n", "df.show()"]}, {"cell_type": "code", "execution_count": null, "id": "create_iceberg_table", "metadata": {}, "outputs": [], "source": ["# Create Iceberg table\n", "print(\"🏗️  Creating Iceberg table...\")\n", "\n", "# Drop table if exists\n", "spark.sql(\"DROP TABLE IF EXISTS local.demo.employees\")\n", "\n", "# Create Iceberg table with partitioning\n", "create_table_sql = \"\"\"\n", "CREATE TABLE local.demo.employees (\n", "    id INT,\n", "    name STRING,\n", "    department STRING,\n", "    salary INT,\n", "    hire_date DATE,\n", "    created_at TIMESTAMP\n", ")\n", "USING iceberg\n", "PARTITIONED BY (department)\n", "\"\"\"\n", "\n", "spark.sql(create_table_sql)\n", "print(\"✅ Iceberg table 'employees' created successfully\")"]}, {"cell_type": "code", "execution_count": null, "id": "insert_data", "metadata": {}, "outputs": [], "source": ["# Insert data into Iceberg table\n", "print(\"📥 Inserting data into Iceberg table...\")\n", "\n", "df.write.mode(\"append\").saveAsTable(\"local.demo.employees\")\n", "\n", "print(\"✅ Data inserted successfully\")\n", "\n", "# Verify data\n", "result = spark.sql(\"SELECT * FROM local.demo.employees\")\n", "print(f\"📊 Table now contains {result.count()} records\")\n", "result.show()"]}, {"cell_type": "code", "execution_count": null, "id": "iceberg_features", "metadata": {}, "outputs": [], "source": ["# Demonstrate Iceberg features\n", "print(\"🔍 Demonstrating Iceberg features...\")\n", "\n", "# Show table metadata\n", "print(\"\\n📋 Table metadata:\")\n", "spark.sql(\"DESCRIBE EXTENDED local.demo.employees\").show(truncate=False)\n", "\n", "# Show partitions\n", "print(\"\\n📂 Table partitions:\")\n", "spark.sql(\"SHOW PARTITIONS local.demo.employees\").show()\n", "\n", "# Show table history (Iceberg time travel)\n", "print(\"\\n🕐 Table history:\")\n", "spark.sql(\"SELECT * FROM local.demo.employees.history\").show(truncate=False)"]}, {"cell_type": "code", "execution_count": null, "id": "analytics_example", "metadata": {}, "outputs": [], "source": ["# Perform some analytics\n", "print(\"📈 Performing analytics...\")\n", "\n", "# Department-wise salary analysis\n", "print(\"\\n💰 Salary by Department:\")\n", "dept_analysis = spark.sql(\"\"\"\n", "    SELECT \n", "        department,\n", "        COUNT(*) as employee_count,\n", "        AVG(salary) as avg_salary,\n", "        MIN(salary) as min_salary,\n", "        MAX(salary) as max_salary\n", "    FROM local.demo.employees\n", "    GROUP BY department\n", "    ORDER BY avg_salary DESC\n", "\"\"\")\n", "dept_analysis.show()\n", "\n", "# Recent hires\n", "print(\"\\n👥 Recent Hires (2023):\")\n", "recent_hires = spark.sql(\"\"\"\n", "    SELECT name, department, salary, hire_date\n", "    FROM local.demo.employees\n", "    WHERE year(hire_date) = 2023\n", "    ORDER BY hire_date DESC\n", "\"\"\")\n", "recent_hires.show()"]}, {"cell_type": "code", "execution_count": null, "id": "update_data", "metadata": {}, "outputs": [], "source": ["# Demonstrate Iceberg ACID operations\n", "print(\"🔄 Demonstrating ACID operations...\")\n", "\n", "# Update salaries (give everyone a 5% raise)\n", "print(\"\\n💰 Giving everyone a 5% salary increase...\")\n", "spark.sql(\"\"\"\n", "    UPDATE local.demo.employees \n", "    SET salary = salary * 1.05\n", "\"\"\")\n", "\n", "# Show updated data\n", "print(\"✅ Salaries updated! New salary data:\")\n", "spark.sql(\"SELECT name, department, salary FROM local.demo.employees ORDER BY salary DESC\").show()\n", "\n", "# Add a new employee\n", "print(\"\\n👤 Adding a new employee...\")\n", "new_employee = [(6, \"<PERSON>\", \"HR\", 60000, \"2023-04-01\", current_timestamp())]\n", "new_df = spark.createDataFrame(new_employee, schema=df.schema)\n", "new_df.write.mode(\"append\").saveAsTable(\"local.demo.employees\")\n", "\n", "print(\"✅ New employee added!\")\n", "final_count = spark.sql(\"SELECT COUNT(*) as total FROM local.demo.employees\").collect()[0]['total']\n", "print(f\"📊 Total employees: {final_count}\")"]}, {"cell_type": "code", "execution_count": null, "id": "cleanup", "metadata": {}, "outputs": [], "source": ["# Show final table state and cleanup options\n", "print(\"🏁 Final table state:\")\n", "spark.sql(\"SELECT * FROM local.demo.employees ORDER BY id\").show()\n", "\n", "print(\"\\n🕐 Updated table history:\")\n", "spark.sql(\"SELECT * FROM local.demo.employees.history\").show(truncate=False)\n", "\n", "print(\"\\n📁 Warehouse location: /home/<USER>/warehouse\")\n", "print(\"🔗 REST Catalog: http://rest:8181\")\n", "print(\"\\n✅ Demo completed successfully!\")\n", "print(\"   You now have a working Spark-Iceberg setup on local filesystem\")\n", "print(\"   optimized for 6GB RAM Windows 10 with Docker.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 5}