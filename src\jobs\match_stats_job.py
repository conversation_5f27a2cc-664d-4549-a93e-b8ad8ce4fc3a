from pyspark.sql import SparkSession
from pyspark.sql.functions import broadcast
import shutil

def broadcast_join_maps(spark):
    # creating maps and matches DataFrames
    matches_df = spark.read.csv("..\..\data\matches.csv", header=True, inferSchema=True)
    maps_df = spark.read.csv("..\..\data\maps.csv", header=True, inferSchema=True)
 
    # broadcast join maps_df with matches_df
    matches_maps_df = matches_df.join(broadcast(maps_df), "mapid")
    
    return matches_maps_df

def broadcast_join_medals(spark):
    # creating medals and medal_matches_players DataFrames
    medals_df = spark.read.csv("..\..\data\medals.csv", header=True, inferSchema=True)
    medals_matches_players_df = spark.read.csv("..\..\data\medals_matches_players.csv", header=True, inferSchema=True)
 
   # broadcast join medal_df with medal_matches_players_df

   medals_players_df = medals_matches_players_df.join(broadcast(medals_df), "medalid")

   return medals_players_df

def bucket_join_matches(spark):
    num_of_buckets = 16

    # creating matches DataFrames
    matches_df = spark.read.csv("..\..\data\matches.csv", header=True, inferSchema=True)
    
    #repartition and sort within partition
    spark.sql("DROP TABLE IF EXISTS matches_bucketed")
    matches_df = matches_df.repartition(num_of_buckets, "match_id").sortWithinPartitions("match_id")
        .write.format("parquet").bucketBy(num_of_buckets, "match_id").mode("overwrite").saveAsTable("matches_bucketed") \
    
    #read bucket table
    matches_bucketed_df = spark.table("matches_bucketed")
    return matches_bucketed_df

def bucket_join_match_details(spark):
    num_of_buckets = 16

    # creating matches and match details DataFrames
    match_details_df = spark.read.csv("..\..\data\match_details.csv", header=True, inferSchema=True)
    
    #repartition and sort within partition
    spark.sql("DROP TABLE IF EXISTS match_details_bucketed")
    match_details_df = match_details_df.repartition(num_of_buckets, "match_id").sortWithinPartitions("match_id") \
        .write.format("parquet").mode("overwrite").saveAsTable("match_details_bucketed")
    
    #read bucket table
    match_details_bucketed_df = spark.table("match_details_bucketed")
    return match_details_bucketed_df

def bucket_join_medal_matches_players(spark):
    num_of_buckets = 16

    # creating medal matches players DataFrames
    medal_matches_players_df = spark.read.csv("..\..\data\medal_matches_players.csv", header=True, inferSchema=True)
    
    #repartition and sort within partitions
    medal_matches_players_df = medal_matches_players_df.repartition(num_of_buckets, "match_id").sortwithpartitions("match_id") \
        .write.format("parquet").mode("overwrite").saveAsTable("match_details_bucketed")
    
    #read bucket table
    medal_matches_players_bucketed_df = spark.table("medal_matches_players_bucketed")
    medal_matches_players_bucketed_df.show()
    return medal_matches_players_bucketed_df

def bucket_join_everything(matches_df, match_details_df, medal_matches_players_df):
    # bucket join all dataframes
    bucketed_df = matches_df.join(match_details_df, "match_id").join(medal_matches_players_df, "match_id")
    return bucketed_df

def get_aggregated_stats(spark, df):
    start_time = time.time() #record the start time

    #get the average kills per game for each player
    df.groupBy("player_id", "player_gamertag").avg("player_total_kills").alias("average_kills_per_game).show()

    #get the most common playlsit
    most_common_playlist = df_groupby ("playlist_id").count().orderBy("count", ascendingFalse).take(1)
    print("The most common playlist is: ", most_common_playlist)

    # get the most common map
    most_common_map = df_groupby ("mapid").count().orderBy("count", ascendingFalse).take(1)
    print("The most common map is: ", most_common_map)

    # get the most common map for medal classification killing spree
    medal_df = spark.read.csv("..\..\data\medals.csv", header=True, inferSchema=True)
    df = df.join(broadcast(medal_df), "medalid")
    killing_spree = df.filter(df.classification == "killing spree").groupBy("mapid").count().orderBy("count", ascending=False).take(1)
    print("The most common map for killing spree is: ", killing_spree)

    end_time = time.time() #record the end time

    print(f"Time taken to get aggregated stats: {end_time - start_time} seconds")


def bucket_join_matches_v2(spark):
    num_of_buckets = 16

    # creating matches DataFrames
    matches_df = spark.read.csv("..\..\data\matches.csv", header=True, inferSchema=True)
    
    #repartition and sort within partition
    matches_df = matches_df.repartition(num_of_buckets, "match_id").sortWithinPartitions("match_id", "mapid").write \
        .format("parquet").mode("overwrite").save("matches_bucketed_v2")
    
    #read bucket table
    matches_bucketed_df = spark.table("matches_bucketed")
    return matches_bucketed_df

def bucket_join_matches_v3(spark):
    num_of_buckets = 16

    # create matches DataFrames
    matches_df = spark.read.csv("..\..\data\matches.csv", header=True, inferSchema=True)

    # repartition and sortWithinPartition
    matches_df = matches_df.repartition(num_of_buckets, "match_id").sortWithinPartitions("matchid", "playlist_id") \
        .write.format("parquet").mode("overwrite").saveAsTable("matches_bucketed_v3")


    # compare match_bucketed, match_bucketed_v2, and match_bucketed_v3 file sizes
    compare_file_sizes()

    # read bucket table
    matches_bucketed_df = spark.table("matches_bucketed_v3")
    return matches_bucketed_df

def bucket_join_matches_v4(spark):
    num_of_buckets = 16

    # create matches DataFrames
    matches_df = spark.read.csv("..\..\data\matches.csv", header=True, inferSchema=True)

    # repartition and sortWithinPartition
    matches_df = matches_df.repartition(num_of_buckets, "match_id").sortWithinPartitions("matchid", "playlist_id", "mapid") \
        .write.format("parquet").mode("overwrite").saveAsTable("matches_bucketed_v4")

    # read bucketed table
    matches_bucketed_df = spark.table("matches_bucketed_v4")
    return matches_bucketed_df

def compare_file_sizes():
    #compare the file sizes of the three bucketed dataframes
     tables = ["matches_bucketed", "matches_bucketed_v2", "matches_bucketed_v3", "matches_bucketed_v4"]

        for table in tables:
            file_path = "spark-warehouse/" + table
            file_sizes = []
            if os.path.exists(file_path):
                for file in os.listdir(file_path):
                    file_sizes.append(os.path.getsize(file_path + "/" + file))
                print(f"Table: {table}, File Sizes: {sorted(file_sizes)}")

def verify_table_correctness(spark):
    tables = ["matches_bucketed", "match_details_bucketed", "medal_matches_players_bucketed"]
    for table in tables:
        df = spark.table(table)
        print(f"Partitions for {table}: {df.rdd.getNumPartitions()}")
        print(f"Number of records for {table}: {df.count()}")
        df.show()

def main():

    #creating a sparkseason
    spark = SparkSession.builder \
        .config("spark.sql.autoBroadcastJoinThreshold", -1) \
        .appName("MatchStats") \
        .getOrCreate()

    # broadcast join maps and matches dataframes
    broadcast_map = broadcast_join_maps(spark)


    # broadcast join medals and medal_matches_players
    broadcast_medals = broadcast_join_medals(spark)

    
    # bucket join matches dataframe
    bucket_matches = bucket_join_matches(spark)

    # bucket join match_details dataframe
    bucket_match_details = bucket_join_match_details(spark)

    # bucket join medals_matches_players dateframe
    bucketed_medals_matches_players = bucket_join_matches_players(spark)

    # bucket join all dataframes
    bucketed_df = bucket_join_everything(bucketed_matches, bucketed_match_details, bucketed_medals_matches_players)


    # get aggregated stats
    get_aggregated_stats(spark, bucketed_df)

    # bucket join matches dataframe v2
    bucket_matches_v2 = bucket_join_matches_v2(spark)
    bucket_df_v2 = bucket_join_everything(bucket_matches_v2, bucketed_match_details, bucketed_medals_matches_players)

   
    #get aggregated stats
    get_aggregated_stats(spark, bucket_df_v2)

    # bucket join matches dataframe v3
    bucket_matches_v3 = bucket_join_matches_v3(spark)
    bucket_df_v3 = bucket_join_everything(bucket_matches_v3, bucketed_match_details, bucketed_medals_matches_players)

    #get aggregated stats
    get_aggregated_stats(spark, bucket_df_v3)

    # bucket join matches dataframe v4
    bucket_matches_v4 = bucket_join_matches_v4(spark)
    bucket_df_v4 = bucket_join_everything(bucket_matches_v4, bucketed_match_details, bucketed_medals_matches_players)

    #get aggregated stats
    get_aggregated_stats(spark, bucket_df_v4)

    #compare file sizes
    compare_file_sizes()

    #verify table correctness
    verify_table_correctness(spark)

  
if __name__ == "__main__":
    main()