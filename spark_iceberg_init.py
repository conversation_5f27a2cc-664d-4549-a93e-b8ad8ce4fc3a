"""
Spark-Iceberg Initialization Script for Local Development
Optimized for 6GB RAM Windows 10 system with Docker
"""

from pyspark.sql import SparkSession
from pyspark.conf import SparkConf
import os

def create_spark_session(app_name="LocalIcebergApp"):
    """
    Create an optimized Spark session for local Iceberg development
    """
    
    # Spark configuration optimized for 6GB RAM system
    conf = SparkConf()
    
    # Basic Spark settings
    conf.set("spark.app.name", app_name)
    conf.set("spark.master", "local[2]")  # Use 2 cores for better performance
    
    # Memory settings (conservative for 6GB system)
    conf.set("spark.driver.memory", "1g")
    conf.set("spark.executor.memory", "1g")
    conf.set("spark.driver.maxResultSize", "512m")
    
    # Performance optimizations
    conf.set("spark.sql.adaptive.enabled", "true")
    conf.set("spark.sql.adaptive.coalescePartitions.enabled", "true")
    conf.set("spark.sql.adaptive.coalescePartitions.minPartitionNum", "1")
    conf.set("spark.sql.adaptive.advisoryPartitionSizeInBytes", "64MB")
    conf.set("spark.sql.adaptive.skewJoin.enabled", "true")
    
    # Serialization
    conf.set("spark.serializer", "org.apache.spark.serializer.KryoSerializer")
    
    # JVM settings for low memory
    conf.set("spark.driver.extraJavaOptions", 
             "-Xms512m -Xmx1g -XX:+UseG1GC -XX:MaxGCPauseMillis=200")
    conf.set("spark.executor.extraJavaOptions", 
             "-Xms512m -Xmx1g -XX:+UseG1GC -XX:MaxGCPauseMillis=200")
    
    # Iceberg extensions and catalogs
    conf.set("spark.sql.extensions", 
             "org.apache.iceberg.spark.extensions.IcebergSparkSessionExtensions")
    
    # Configure catalogs
    conf.set("spark.sql.catalog.spark_catalog", 
             "org.apache.iceberg.spark.SparkSessionCatalog")
    conf.set("spark.sql.catalog.spark_catalog.type", "hive")
    
    # Local REST catalog
    conf.set("spark.sql.catalog.local", 
             "org.apache.iceberg.spark.SparkCatalog")
    conf.set("spark.sql.catalog.local.type", "rest")
    conf.set("spark.sql.catalog.local.uri", "http://rest:8181")
    conf.set("spark.sql.catalog.local.warehouse", "file:///home/<USER>/warehouse")
    
    # Hadoop configuration for local filesystem
    conf.set("spark.hadoop.fs.defaultFS", "file:///")
    conf.set("spark.sql.warehouse.dir", "file:///home/<USER>/warehouse")
    
    # File and compression settings
    conf.set("spark.sql.files.maxPartitionBytes", "128MB")
    conf.set("spark.sql.parquet.compression.codec", "snappy")
    conf.set("spark.sql.parquet.enableVectorizedReader", "true")
    
    # Network timeouts
    conf.set("spark.network.timeout", "300s")
    conf.set("spark.sql.broadcastTimeout", "300")
    
    # UI settings
    conf.set("spark.ui.enabled", "true")
    conf.set("spark.ui.port", "4040")
    
    # Create Spark session
    spark = SparkSession.builder.config(conf=conf).getOrCreate()
    
    # Set log level to reduce noise
    spark.sparkContext.setLogLevel("WARN")
    
    print(f"✅ Spark session created successfully!")
    print(f"📊 Spark UI available at: http://localhost:4040")
    print(f"🗄️  Warehouse location: file:///home/<USER>/warehouse")
    print(f"🔗 REST Catalog: http://rest:8181")
    print(f"💾 Available memory: Driver={conf.get('spark.driver.memory')}, Executor={conf.get('spark.executor.memory')}")
    
    return spark

def setup_iceberg_database(spark, database_name="bootcamp"):
    """
    Create the default database for Iceberg tables
    """
    try:
        spark.sql(f"CREATE DATABASE IF NOT EXISTS local.{database_name}")
        spark.sql(f"USE local.{database_name}")
        print(f"✅ Database 'local.{database_name}' created and set as default")
    except Exception as e:
        print(f"⚠️  Warning: Could not create database: {e}")
        print("   This might be normal if the REST catalog is not yet available")

def test_spark_setup(spark):
    """
    Test basic Spark functionality
    """
    try:
        # Test basic DataFrame operations
        test_data = [(1, "test", 100), (2, "data", 200)]
        df = spark.createDataFrame(test_data, ["id", "name", "value"])
        count = df.count()
        print(f"✅ Basic Spark test passed - DataFrame count: {count}")
        
        # Test SQL functionality
        df.createOrReplaceTempView("test_table")
        result = spark.sql("SELECT COUNT(*) as total FROM test_table").collect()[0]['total']
        print(f"✅ SQL test passed - SQL count: {result}")
        
        return True
    except Exception as e:
        print(f"❌ Spark test failed: {e}")
        return False

# Main initialization function
def initialize_spark_iceberg(app_name="LocalIcebergApp", database_name="bootcamp"):
    """
    Complete initialization of Spark with Iceberg for local development
    """
    print("🚀 Initializing Spark-Iceberg for local development...")
    print("=" * 60)
    
    # Create Spark session
    spark = create_spark_session(app_name)
    
    # Test basic functionality
    if test_spark_setup(spark):
        print("=" * 60)
        print("🎉 Spark-Iceberg initialization completed successfully!")
        print("   You can now use Spark with Iceberg tables on local filesystem")
        print("   Example: spark.sql('CREATE TABLE local.bootcamp.my_table ...')")
    else:
        print("=" * 60)
        print("⚠️  Spark initialization completed with warnings")
    
    return spark

# For direct execution
if __name__ == "__main__":
    spark = initialize_spark_iceberg()
