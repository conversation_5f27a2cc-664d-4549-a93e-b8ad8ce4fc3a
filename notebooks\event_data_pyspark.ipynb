{"cells": [{"cell_type": "code", "execution_count": 2, "id": "81cca085-dba2-42eb-a13b-fa64b6e86583", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["+-----------+----------+--------+--------------------+----------+--------------------+-------------------+\n", "|    user_id| device_id|referrer|                host|       url|          event_time|         event_date|\n", "+-----------+----------+--------+--------------------+----------+--------------------+-------------------+\n", "| 1037710827| 532630305|    NULL| www.zachwilson.tech|         /|2021-03-08 17:27:...|2021-03-08 00:00:00|\n", "|  925588856| 532630305|    NULL|    www.eczachly.com|         /|2021-05-10 11:26:...|2021-05-10 00:00:00|\n", "|-1180485268| 532630305|    NULL|admin.zach<PERSON><PERSON>....|         /|2021-02-17 16:19:...|2021-02-17 00:00:00|\n", "|-1044833855| 532630305|    NULL| www.zachwilson.tech|         /|2021-09-24 15:53:...|2021-09-24 00:00:00|\n", "|  747494706| 532630305|    NULL| www.zachwilson.tech|         /|2021-09-26 16:03:...|2021-09-26 00:00:00|\n", "|  747494706| 532630305|    NULL|admin.zachwi<PERSON>....|         /|2021-02-21 16:08:...|2021-02-21 00:00:00|\n", "| -824540328| 532630305|    NULL|admin.zach<PERSON><PERSON>....|         /|2021-09-28 17:23:...|2021-09-28 00:00:00|\n", "| -824540328| 532630305|    NULL|    www.eczachly.com|         /|2021-09-29 01:22:...|2021-09-29 00:00:00|\n", "| 1833036683| 532630305|    NULL|admin.zachwi<PERSON>....|         /|2021-01-24 03:15:...|2021-01-24 00:00:00|\n", "|-2134824313| 532630305|    NULL|    www.eczachly.com|         /|2021-01-25 00:03:...|2021-01-25 00:00:00|\n", "|-1809929467|-906264142|    NULL|admin.zach<PERSON><PERSON>....|/.git/HEAD|2021-02-22 01:36:...|2021-02-22 00:00:00|\n", "| 2002285749|-906264142|    NULL|    www.eczachly.com|         /|2021-02-22 02:25:...|2021-02-22 00:00:00|\n", "|-1562965412| 532630305|    NULL| www.zachwilson.tech|         /|2021-01-30 20:46:...|2021-01-30 00:00:00|\n", "|-1099860451| 532630305|    NULL|    www.eczachly.com|         /|2021-02-04 23:49:...|2021-02-04 00:00:00|\n", "| 1246896869|-906264142|    NULL| www.zachwilson.tech|         /|2021-02-22 02:50:...|2021-02-22 00:00:00|\n", "| -629331502|-906264142|    NULL|admin.zach<PERSON><PERSON>....|/.git/HEAD|2021-02-22 23:51:...|2021-02-22 00:00:00|\n", "|-1913422462|-906264142|    NULL|    www.eczachly.com|         /|2021-02-23 00:17:...|2021-02-23 00:00:00|\n", "|   50429624| 532630305|    NULL|    www.eczachly.com|         /|2022-12-28 01:38:...|2022-12-28 00:00:00|\n", "|  222389292| 532630305|    NULL| www.zachwilson.tech|         /|2022-12-28 05:23:...|2022-12-28 00:00:00|\n", "| -779924777| 532630305|    NULL| www.zachwilson.tech|         /|2022-12-28 16:45:...|2022-12-28 00:00:00|\n", "+-----------+----------+--------+--------------------+----------+--------------------+-------------------+\n", "only showing top 20 rows\n", "\n"]}], "source": ["# Import the optimized Spark-Iceberg initialization\n", "import sys\n", "sys.path.append('/home/<USER>')\n", "from spark_iceberg_init import initialize_spark_iceberg\n", "from pyspark.sql.functions import expr, col\n", "\n", "# Initialize Spark with optimized settings for 6GB RAM\n", "spark = initialize_spark_iceberg(app_name=\"EventDataAnalysis\")\n", "\n", "# Load and process the events data\n", "print(\"📁 Loading events data...\")\n", "df = spark.read.option(\"header\", \"true\").csv(\"/home/<USER>/data/events.csv\") \\\n", "    .withColumn(\"event_date\", expr(\"DATE_TRUNC('day', event_time)\"))\n", "\n", "print(f\"📊 Loaded {df.count()} events\")\n", "df.show()"]}, {"cell_type": "code", "execution_count": 3, "id": "dce068df-3e21-429a-8716-abdd13e9406c", "metadata": {}, "outputs": [{"ename": "AnalysisException", "evalue": "[UNRESOLVED_COLUMN.WITH_SUGGESTION] A column or function parameter with name `browser_family` cannot be resolved. Did you mean one of the following? [`user_id`, `referrer`, `device_id`, `event_date`, `event_time`].;\n'Sort [event_date#30 ASC NULLS FIRST, host#21 ASC NULLS FIRST, 'browser_family ASC NULLS FIRST], false\n+- RepartitionByExpression [event_date#30], 10\n   +- Project [user_id#18, device_id#19, referrer#20, host#21, url#22, event_time#23, date_trunc(day, cast(event_time#23 as timestamp), Some(Etc/UTC)) AS event_date#30]\n      +- Relation [user_id#18,device_id#19,referrer#20,host#21,url#22,event_time#23] csv\n", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAnalysisException\u001b[0m                         Traceback (most recent call last)", "Cell \u001b[0;32mIn[3], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[38;5;28msorted\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[43mdf\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrepartition\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m10\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcol\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mevent_date\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[43m\\\u001b[49m\n\u001b[1;32m      2\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msortWithinPartitions\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcol\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mevent_date\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcol\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mhost\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcol\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mbrowser_family\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m \\\n\u001b[1;32m      3\u001b[0m         \u001b[38;5;241m.\u001b[39mwithColumn(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mevent_time\u001b[39m\u001b[38;5;124m\"\u001b[39m, col(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mevent_time\u001b[39m\u001b[38;5;124m\"\u001b[39m)\u001b[38;5;241m.\u001b[39mcast(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtimestamp\u001b[39m\u001b[38;5;124m\"\u001b[39m)) \\\n\u001b[1;32m      5\u001b[0m \u001b[38;5;28msorted\u001b[39m\u001b[38;5;241m.\u001b[39mshow()\n", "File \u001b[0;32m/opt/spark/python/pyspark/sql/dataframe.py:2646\u001b[0m, in \u001b[0;36mDataFrame.sortWithinPartitions\u001b[0;34m(self, *cols, **kwargs)\u001b[0m\n\u001b[1;32m   2613\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21msortWithinPartitions\u001b[39m(\n\u001b[1;32m   2614\u001b[0m     \u001b[38;5;28mself\u001b[39m, \u001b[38;5;241m*\u001b[39mcols: Union[\u001b[38;5;28mstr\u001b[39m, Column, List[Union[\u001b[38;5;28mstr\u001b[39m, Column]]], \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs: Any\n\u001b[1;32m   2615\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mDataFrame\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[1;32m   2616\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Returns a new :class:`DataFrame` with each partition sorted by the specified column(s).\u001b[39;00m\n\u001b[1;32m   2617\u001b[0m \n\u001b[1;32m   2618\u001b[0m \u001b[38;5;124;03m    .. versionadded:: 1.6.0\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   2644\u001b[0m \u001b[38;5;124;03m    DataFrame[age: bigint, name: string]\u001b[39;00m\n\u001b[1;32m   2645\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m-> 2646\u001b[0m     jdf \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_jdf\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msortWithinPartitions\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_sort_cols\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcols\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   2647\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m DataFrame(jdf, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msparkSession)\n", "File \u001b[0;32m/opt/spark/python/lib/py4j-********-src.zip/py4j/java_gateway.py:1322\u001b[0m, in \u001b[0;36mJavaMember.__call__\u001b[0;34m(self, *args)\u001b[0m\n\u001b[1;32m   1316\u001b[0m command \u001b[38;5;241m=\u001b[39m proto\u001b[38;5;241m.\u001b[39mCALL_COMMAND_NAME \u001b[38;5;241m+\u001b[39m\\\n\u001b[1;32m   1317\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcommand_header \u001b[38;5;241m+\u001b[39m\\\n\u001b[1;32m   1318\u001b[0m     args_command \u001b[38;5;241m+\u001b[39m\\\n\u001b[1;32m   1319\u001b[0m     proto\u001b[38;5;241m.\u001b[39mEND_COMMAND_PART\n\u001b[1;32m   1321\u001b[0m answer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mgateway_client\u001b[38;5;241m.\u001b[39msend_command(command)\n\u001b[0;32m-> 1322\u001b[0m return_value \u001b[38;5;241m=\u001b[39m \u001b[43mget_return_value\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1323\u001b[0m \u001b[43m    \u001b[49m\u001b[43manswer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgateway_client\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtarget_id\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mname\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1325\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m temp_arg \u001b[38;5;129;01min\u001b[39;00m temp_args:\n\u001b[1;32m   1326\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mhasattr\u001b[39m(temp_arg, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m_detach\u001b[39m\u001b[38;5;124m\"\u001b[39m):\n", "File \u001b[0;32m/opt/spark/python/pyspark/errors/exceptions/captured.py:185\u001b[0m, in \u001b[0;36mcapture_sql_exception.<locals>.deco\u001b[0;34m(*a, **kw)\u001b[0m\n\u001b[1;32m    181\u001b[0m converted \u001b[38;5;241m=\u001b[39m convert_exception(e\u001b[38;5;241m.\u001b[39mjava_exception)\n\u001b[1;32m    182\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(converted, UnknownException):\n\u001b[1;32m    183\u001b[0m     \u001b[38;5;66;03m# Hide where the exception came from that shows a non-Pythonic\u001b[39;00m\n\u001b[1;32m    184\u001b[0m     \u001b[38;5;66;03m# JVM exception message.\u001b[39;00m\n\u001b[0;32m--> 185\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m converted \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\n\u001b[1;32m    186\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m:\n\u001b[1;32m    187\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m\n", "\u001b[0;31mAnalysisException\u001b[0m: [UNRESOLVED_COLUMN.WITH_SUGGESTION] A column or function parameter with name `browser_family` cannot be resolved. Did you mean one of the following? [`user_id`, `referrer`, `device_id`, `event_date`, `event_time`].;\n'Sort [event_date#30 ASC NULLS FIRST, host#21 ASC NULLS FIRST, 'browser_family ASC NULLS FIRST], false\n+- RepartitionByExpression [event_date#30], 10\n   +- Project [user_id#18, device_id#19, referrer#20, host#21, url#22, event_time#23, date_trunc(day, cast(event_time#23 as timestamp), Some(Etc/UTC)) AS event_date#30]\n      +- Relation [user_id#18,device_id#19,referrer#20,host#21,url#22,event_time#23] csv\n"]}], "source": ["# Fix: Remove non-existent browser_family column and optimize for low memory\n", "print(\"🔄 Processing and sorting data...\")\n", "sorted_df = df.repartition(4, col(\"event_date\")) \\\n", "        .sortWithinPartitions(col(\"event_date\"), col(\"host\")) \\\n", "        .withColumn(\"event_time\", col(\"event_time\").cast(\"timestamp\"))\n", "\n", "print(\"📋 Showing sorted data:\")\n", "sorted_df.show()"]}, {"cell_type": "code", "execution_count": null, "id": "d800dca7-2737-4192-b5c0-c1806c105e15", "metadata": {}, "outputs": [], "source": ["# Create database using local catalog (filesystem-based)\n", "print(\"🗄️  Creating local database...\")\n", "spark.sql(\"CREATE DATABASE IF NOT EXISTS local.bootcamp\")\n", "spark.sql(\"USE local.bootcamp\")\n", "print(\"✅ Database 'local.bootcamp' created and set as default\")\n", "\n", "# Show available databases\n", "print(\"📋 Available databases:\")\n", "spark.sql(\"SHOW DATABASES\").show()"]}, {"cell_type": "code", "execution_count": null, "id": "e83cd813-d5c0-4d67-8285-849b882b8bfa", "metadata": {}, "outputs": [], "source": ["# Drop existing table if it exists\n", "print(\"🗑️  Dropping existing events table if it exists...\")\n", "spark.sql(\"DROP TABLE IF EXISTS local.bootcamp.events\")\n", "print(\"✅ Table dropped (if it existed)\")"]}, {"cell_type": "code", "execution_count": null, "id": "d1b197a9-1b63-4130-acbe-01418eede0e5", "metadata": {"scrolled": true}, "outputs": [], "source": ["# Create Iceberg table with local catalog\n", "print(\"🏗️  Creating Iceberg events table...\")\n", "create_table_sql = \"\"\"\n", "CREATE TABLE IF NOT EXISTS local.bootcamp.events (\n", "    user_id STRING,\n", "    device_id STRING,\n", "    url STRING,\n", "    referrer STRING,\n", "    host STRING,\n", "    event_time TIMESTAMP,\n", "    event_date DATE\n", ")\n", "USING iceberg\n", "PARTITIONED BY (years(event_date))\n", "\"\"\"\n", "\n", "spark.sql(create_table_sql)\n", "print(\"✅ Iceberg events table created successfully\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "c40b143f-295e-4875-bd7f-12409312b800", "metadata": {"scrolled": true}, "outputs": [], "source": ["%%sql\n", "\n", "\n", "CREATE TABLE IF NOT EXISTS bootcamp.events_sorted (\n", "    url STRING,\n", "    referrer STRING,\n", "    browser_family STRING,\n", "    os_family STRING,\n", "    device_family STRING,\n", "    host STRING,\n", "    event_time TIMESTAMP,\n", "    event_date DATE\n", ")\n", "USING iceberg;"]}, {"cell_type": "code", "execution_count": null, "id": "00c86e79-a911-464c-ad58-acc92859dcc6", "metadata": {"scrolled": true}, "outputs": [], "source": ["%%sql\n", "\n", "\n", "CREATE TABLE IF NOT EXISTS bootcamp.events_unsorted (\n", "    url STRING,\n", "    referrer STRING,\n", "    browser_family STRING,\n", "    os_family STRING,\n", "    device_family STRING,\n", "    host STRING,\n", "    event_time TIMESTAMP,\n", "    event_date DATE\n", ")\n", "USING iceberg;"]}, {"cell_type": "code", "execution_count": null, "id": "2c1254bc-9ecf-4c86-bfd9-de81ecfbb78b", "metadata": {}, "outputs": [], "source": ["\n", "start_df = df.repartition(4, col(\"event_date\")).withColumn(\"event_time\", col(\"event_time\").cast(\"timestamp\")) \\\n", "    \n", "\n", "first_sort_df = start_df.sortWithinPartitions(col(\"event_date\"), col(\"browser_family\"), col(\"host\"))\n", "\n", "sorted = df.repartition(10, col(\"event_date\")) \\\n", "        .sortWithinPartitions(col(\"event_date\")) \\\n", "        .withColumn(\"event_time\", col(\"event_time\").cast(\"timestamp\")) \\\n", "\n", "start_df.write.mode(\"overwrite\").saveAsTable(\"bootcamp.events_unsorted\")\n", "first_sort_df.write.mode(\"overwrite\").saveAsTable(\"bootcamp.events_sorted\")"]}, {"cell_type": "code", "execution_count": null, "id": "d207a11b-b0a4-4662-bbe9-747d8f67be7d", "metadata": {}, "outputs": [], "source": ["%%sql\n", "\n", "SELECT SUM(file_size_in_bytes) as size, COUNT(1) as num_files, 'sorted' \n", "FROM demo.bootcamp.events_sorted.files\n", "\n", "UNION ALL\n", "SELECT SUM(file_size_in_bytes) as size, COUNT(1) as num_files, 'unsorted' \n", "FROM demo.bootcamp.events_unsorted.files\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "a93db4d6-ac15-4d0e-83da-77b93ad618da", "metadata": {}, "outputs": [], "source": ["%%sql\n", "SELECT SUM(file_size_in_bytes) as size, COUNT(1) as num_files FROM demo.bootcamp.events.files;"]}, {"cell_type": "code", "execution_count": 1, "id": "0e7c057e", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}, "pycharm": {"name": "#%%\n"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["24/12/16 12:56:46 WARN SparkSession: Using an existing Spark session; only runtime SQL configurations will take effect.\n"]}, {"ename": "AnalysisException", "evalue": "[TABLE_OR_VIEW_NOT_FOUND] The table or view `bootcamp`.`matches_bucketed`.`files` cannot be found. Verify the spelling and correctness of the schema and catalog.\nIf you did not qualify the name with a schema, verify the current_schema() output, or qualify the name with the correct schema and catalog.\nTo tolerate the error on drop use DROP VIEW IF EXISTS or DROP TABLE IF EXISTS.; line 1 pos 21;\n'Aggregate [unresolvedalias(count(1), None)]\n+- 'UnresolvedRelation [bootcamp, matches_bucketed, files], [], false\n", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAnalysisException\u001b[0m                         Traceback (most recent call last)", "Cell \u001b[0;32mIn[1], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[43mget_ipython\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrun_cell_magic\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43msql\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mSELECT COUNT(1) FROM bootcamp.matches_bucketed.files\u001b[39;49m\u001b[38;5;130;43;01m\\n\u001b[39;49;00m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/usr/local/lib/python3.9/site-packages/IPython/core/interactiveshell.py:2517\u001b[0m, in \u001b[0;36mInteractiveShell.run_cell_magic\u001b[0;34m(self, magic_name, line, cell)\u001b[0m\n\u001b[1;32m   2515\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mbuiltin_trap:\n\u001b[1;32m   2516\u001b[0m     args \u001b[38;5;241m=\u001b[39m (magic_arg_s, cell)\n\u001b[0;32m-> 2517\u001b[0m     result \u001b[38;5;241m=\u001b[39m \u001b[43mfn\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   2519\u001b[0m \u001b[38;5;66;03m# The code below prevents the output from being displayed\u001b[39;00m\n\u001b[1;32m   2520\u001b[0m \u001b[38;5;66;03m# when using magics with decorator @output_can_be_silenced\u001b[39;00m\n\u001b[1;32m   2521\u001b[0m \u001b[38;5;66;03m# when the last Python token in the expression is a ';'.\u001b[39;00m\n\u001b[1;32m   2522\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mgetattr\u001b[39m(fn, magic\u001b[38;5;241m.\u001b[39mMAGIC_OUTPUT_CAN_BE_SILENCED, \u001b[38;5;28;01mFalse\u001b[39;00m):\n", "File \u001b[0;32m~/.ipython/profile_default/startup/00-prettytables.py:81\u001b[0m, in \u001b[0;36msql\u001b[0;34m(line, cell)\u001b[0m\n\u001b[1;32m     79\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m _to_table(df, num_rows\u001b[38;5;241m=\u001b[39margs\u001b[38;5;241m.\u001b[39mlimit)\n\u001b[1;32m     80\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m---> 81\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m _to_table(\u001b[43mspark\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msql\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcell\u001b[49m\u001b[43m)\u001b[49m)\n", "File \u001b[0;32m/opt/spark/python/pyspark/sql/session.py:1631\u001b[0m, in \u001b[0;36mSparkSession.sql\u001b[0;34m(self, sqlQuery, args, **kwargs)\u001b[0m\n\u001b[1;32m   1627\u001b[0m         \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_jvm \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m   1628\u001b[0m         litArgs \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_jvm\u001b[38;5;241m.\u001b[39mPythonUtils\u001b[38;5;241m.\u001b[39mtoArray(\n\u001b[1;32m   1629\u001b[0m             [_to_java_column(lit(v)) \u001b[38;5;28;01mfor\u001b[39;00m v \u001b[38;5;129;01min\u001b[39;00m (args \u001b[38;5;129;01<PERSON>\u001b[39;00m [])]\n\u001b[1;32m   1630\u001b[0m         )\n\u001b[0;32m-> 1631\u001b[0m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m DataFrame(\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_jsparkSession\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msql\u001b[49m\u001b[43m(\u001b[49m\u001b[43msqlQuery\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlitArgs\u001b[49m\u001b[43m)\u001b[49m, \u001b[38;5;28mself\u001b[39m)\n\u001b[1;32m   1632\u001b[0m \u001b[38;5;28;01mfinally\u001b[39;00m:\n\u001b[1;32m   1633\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(kwargs) \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m0\u001b[39m:\n", "File \u001b[0;32m/opt/spark/python/lib/py4j-********-src.zip/py4j/java_gateway.py:1322\u001b[0m, in \u001b[0;36mJavaMember.__call__\u001b[0;34m(self, *args)\u001b[0m\n\u001b[1;32m   1316\u001b[0m command \u001b[38;5;241m=\u001b[39m proto\u001b[38;5;241m.\u001b[39mCALL_COMMAND_NAME \u001b[38;5;241m+\u001b[39m\\\n\u001b[1;32m   1317\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcommand_header \u001b[38;5;241m+\u001b[39m\\\n\u001b[1;32m   1318\u001b[0m     args_command \u001b[38;5;241m+\u001b[39m\\\n\u001b[1;32m   1319\u001b[0m     proto\u001b[38;5;241m.\u001b[39mEND_COMMAND_PART\n\u001b[1;32m   1321\u001b[0m answer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mgateway_client\u001b[38;5;241m.\u001b[39msend_command(command)\n\u001b[0;32m-> 1322\u001b[0m return_value \u001b[38;5;241m=\u001b[39m \u001b[43mget_return_value\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1323\u001b[0m \u001b[43m    \u001b[49m\u001b[43manswer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgateway_client\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtarget_id\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mname\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1325\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m temp_arg \u001b[38;5;129;01min\u001b[39;00m temp_args:\n\u001b[1;32m   1326\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mhasattr\u001b[39m(temp_arg, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m_detach\u001b[39m\u001b[38;5;124m\"\u001b[39m):\n", "File \u001b[0;32m/opt/spark/python/pyspark/errors/exceptions/captured.py:185\u001b[0m, in \u001b[0;36mcapture_sql_exception.<locals>.deco\u001b[0;34m(*a, **kw)\u001b[0m\n\u001b[1;32m    181\u001b[0m converted \u001b[38;5;241m=\u001b[39m convert_exception(e\u001b[38;5;241m.\u001b[39mjava_exception)\n\u001b[1;32m    182\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(converted, UnknownException):\n\u001b[1;32m    183\u001b[0m     \u001b[38;5;66;03m# Hide where the exception came from that shows a non-Pythonic\u001b[39;00m\n\u001b[1;32m    184\u001b[0m     \u001b[38;5;66;03m# JVM exception message.\u001b[39;00m\n\u001b[0;32m--> 185\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m converted \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\n\u001b[1;32m    186\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m:\n\u001b[1;32m    187\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m\n", "\u001b[0;31mAnalysisException\u001b[0m: [TABLE_OR_VIEW_NOT_FOUND] The table or view `bootcamp`.`matches_bucketed`.`files` cannot be found. Verify the spelling and correctness of the schema and catalog.\nIf you did not qualify the name with a schema, verify the current_schema() output, or qualify the name with the correct schema and catalog.\nTo tolerate the error on drop use DROP VIEW IF EXISTS or DROP TABLE IF EXISTS.; line 1 pos 21;\n'Aggregate [unresolvedalias(count(1), None)]\n+- 'UnresolvedRelation [bootcamp, matches_bucketed, files], [], false\n"]}], "source": ["%%sql \n", "SELECT COUNT(1) FROM bootcamp.matches_bucketed.files"]}, {"cell_type": "code", "execution_count": null, "id": "674f173d-86ca-4637-9332-37ce04a81451", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "591ba0b7-2e6f-420a-960d-95e5734b0166", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 5}