from chispa.dataframe_comparer import *
from pyspark.sql import SparkSession
from datetime import datetime

from ..jobs.user_devices_datelist_int import do_user_devices_datelist_int
from collections import namedtuple

user_devices_cumulated = namedtuple("user_devices_cumulated",  "user_id device_id browser_type date device_activity_datelist")
user_devices_datelist_int = namedtuple("user_devices_datelist_int", "user_id device_id browser_type device_activity_datelist datelist_int")

def test_when_user_active_everyday(spark):

    # Arrange
    datelist = []

    for i in range(1, 32):
        datelist.append(datetime.strptime(f"2023-01-{i}", "%Y-%m-%d").date())
    
    user_devices_cumulated_data = [
        user_devices_cumulated(user_id=1, device_id=1, browser_type="Chrome", date=datetime.strptime("2023-01-01", "%Y-%m-%d").date(), device_activity_datelist=datelist),
    ]

    expected_number = int("11111111111111111110", 2)

    expected_data = [
        user_devices_datelist_int(user_id=1, device_id=1, browser_type="Chrome", device_activity_datelist=datelist, datelist_int=4294967295)
    ]

    users_devices_cumulated_df = spark.createDataFrame(user_devices_cumulated_data)
    expected_df = spark.createDataFrame(expected_data)

    # Act
    actual_df = do_user_devices_datelist_int(spark, users_devices_cumulated_df)

    # Assert
    assert_df_equality(actual_df, expected_df)

def test_when_user_active_every_other_day(spark):
        # Arrange
        datelist = []
        for i in range(1, 32, 2):
            datelist.append(datetime.strptime(f"2023-01-{i}", "%Y-%m-%d").date())
    
        user_devices_cumulated_data = [
        user_devices_cumulated(user_id=1, device_id=1, browser_type="Chrome", date=datetime.strptime("2023-01-01", "%Y-%m-%d").date(), device_activity_datelist=datelist),
    ]

    expected_number = int("10101010101010101010101010101010", 2)

    expected_data = [
        user_devices_datelist_int(user_id=1, device_id=1, browser_type="Chrome", device_activity_datelist=datelist, datelist_int=2863311530)
    ]

    users_devices_cumulated_df = spark.createDataFrame(user_devices_cumulated_data)
    expected_df = spark.createDataFrame(expected_data)

    # Act
    actual_df = do_user_devices_datelist_int(spark, users_devices_cumulated_df)

    # Assert
    assert_df_equality(actual_df, expected_df)

    def test_when_user_not_active_in_a_year(spark):

        # Arrange
       last_year = datetime.strptime("2022-01-31", "%Y-%m-%d").date()

       user_devices_cumulated_data = [
        user_devices_cumulated(user_id=1, device_id=1, browser_type="Chrome", date=last_year, device_activity_datelist=[]),
       ]
    expected_number = bin("0000000000000000", 2)

    expected_data = [
        user_devices_datelist_int(user_id=1, device_id=1, browser_type="Chrome", device_activity_datelist=[], datelist_int=0)
    ]

    user_devices_cumulated_df = spark.createDataFrame(user_devices_cumulated_data, schema="user_id int, device_id int, browser_type string, date date, device_activity_datelist array<date>")
    expected_df = spark.createDataFrame(expected_data)

    # Act
    actual_df = do_user_devices_datelist_int(spark, user_devices_cumulated_df)

    # Assert
    assert_df_equality(actual_df, expected_df)
