#!/usr/bin/env python3
"""
Test script to validate Spark-Iceberg local setup
Run this inside the Docker container to verify everything works
"""

import sys
import time
import requests
from spark_iceberg_init import initialize_spark_iceberg

def test_rest_catalog():
    """Test if REST catalog is accessible"""
    print("🔗 Testing REST Catalog connection...")
    try:
        response = requests.get("http://rest:8181/v1/config", timeout=10)
        if response.status_code == 200:
            print("✅ REST Catalog is accessible")
            return True
        else:
            print(f"❌ REST Catalog returned status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ REST Catalog connection failed: {e}")
        return False

def test_spark_session():
    """Test Spark session creation"""
    print("⚡ Testing Spark session creation...")
    try:
        spark = initialize_spark_iceberg("TestApp")
        print("✅ Spark session created successfully")
        
        # Test basic DataFrame operations
        test_data = [(1, "test"), (2, "data")]
        df = spark.createDataFrame(test_data, ["id", "value"])
        count = df.count()
        print(f"✅ Basic DataFrame test passed - count: {count}")
        
        return spark
    except Exception as e:
        print(f"❌ Spark session creation failed: {e}")
        return None

def test_iceberg_operations(spark):
    """Test Iceberg table operations"""
    print("🧊 Testing Iceberg operations...")
    try:
        # Create database
        spark.sql("CREATE DATABASE IF NOT EXISTS local.test")
        spark.sql("USE local.test")
        print("✅ Database created")
        
        # Drop table if exists
        spark.sql("DROP TABLE IF EXISTS local.test.sample")
        
        # Create Iceberg table
        create_sql = """
        CREATE TABLE local.test.sample (
            id INT,
            name STRING,
            created_at TIMESTAMP
        )
        USING iceberg
        """
        spark.sql(create_sql)
        print("✅ Iceberg table created")
        
        # Insert data
        insert_sql = """
        INSERT INTO local.test.sample VALUES 
        (1, 'Alice', current_timestamp()),
        (2, 'Bob', current_timestamp())
        """
        spark.sql(insert_sql)
        print("✅ Data inserted")
        
        # Query data
        result = spark.sql("SELECT * FROM local.test.sample")
        count = result.count()
        print(f"✅ Data query successful - count: {count}")
        
        # Show table history
        history = spark.sql("SELECT * FROM local.test.sample.history")
        history_count = history.count()
        print(f"✅ Table history accessible - entries: {history_count}")
        
        return True
    except Exception as e:
        print(f"❌ Iceberg operations failed: {e}")
        return False

def test_memory_usage():
    """Test memory usage and performance"""
    print("💾 Testing memory usage...")
    try:
        import psutil
        import os
        
        # Get current process memory usage
        process = psutil.Process(os.getpid())
        memory_mb = process.memory_info().rss / 1024 / 1024
        print(f"📊 Current process memory usage: {memory_mb:.1f} MB")
        
        if memory_mb < 2048:  # Less than 2GB
            print("✅ Memory usage is within acceptable limits")
            return True
        else:
            print("⚠️  Memory usage is high but may be acceptable")
            return True
    except ImportError:
        print("⚠️  psutil not available, skipping memory test")
        return True
    except Exception as e:
        print(f"❌ Memory test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Starting Spark-Iceberg Setup Validation")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 4
    
    # Test 1: REST Catalog
    if test_rest_catalog():
        tests_passed += 1
    
    print()
    
    # Test 2: Spark Session
    spark = test_spark_session()
    if spark:
        tests_passed += 1
    
    print()
    
    # Test 3: Iceberg Operations
    if spark and test_iceberg_operations(spark):
        tests_passed += 1
    
    print()
    
    # Test 4: Memory Usage
    if test_memory_usage():
        tests_passed += 1
    
    print()
    print("=" * 50)
    print(f"🏁 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Your Spark-Iceberg setup is working correctly.")
        print("   You can now use Jupyter notebooks at http://localhost:8888")
    elif tests_passed >= 2:
        print("⚠️  Most tests passed. Setup is mostly working but may have some issues.")
    else:
        print("❌ Multiple tests failed. Please check your setup.")
    
    # Cleanup
    if spark:
        try:
            spark.sql("DROP TABLE IF EXISTS local.test.sample")
            spark.sql("DROP DATABASE IF EXISTS local.test")
            spark.stop()
            print("🧹 Cleanup completed")
        except:
            pass
    
    return tests_passed == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
