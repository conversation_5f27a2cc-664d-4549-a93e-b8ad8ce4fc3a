services:
  # Minimal Spark setup with much smaller image (~500MB instead of 1.5GB)
  spark-minimal:
    image: bitnami/spark:3.5.0
    container_name: spark-minimal
    environment:
      - SPARK_MODE=master
      - SPARK_MASTER_HOST=spark-minimal
      - SPARK_MASTER_PORT=7077
      - SPARK_MASTER_WEBUI_PORT=8080
      - SPARK_DRIVER_MEMORY=1g
      - SPARK_EXECUTOR_MEMORY=1g
    ports:
      - "8080:8080"  # Spark Master UI
      - "7077:7077"  # Spark Master
      - "4040:4040"  # Spark Application UI
    volumes:
      - ./warehouse:/opt/warehouse
      - ./data:/opt/data
      - ./notebooks:/opt/notebooks
      - ./src:/opt/src
    deploy:
      resources:
        limits:
          memory: 2g
        reservations:
          memory: 1g
    networks:
      - spark-network

  # Lightweight Jupyter with PySpark (~300MB instead of 1GB+)
  jupyter-minimal:
    image: jupyter/pyspark-notebook:spark-3.5.0
    container_name: jupyter-minimal
    environment:
      - JUPYTER_ENABLE_LAB=yes
      - SPARK_MASTER=spark://spark-minimal:7077
      - PYSPARK_DRIVER_PYTHON=jupyter
      - PYSPARK_DRIVER_PYTHON_OPTS=lab --ip=0.0.0.0 --port=8888 --no-browser --allow-root --NotebookApp.token='' --NotebookApp.password=''
    ports:
      - "8888:8888"  # Jupyter
    volumes:
      - ./warehouse:/home/<USER>/warehouse
      - ./data:/home/<USER>/data
      - ./notebooks:/home/<USER>/notebooks
      - ./src:/home/<USER>/src
    depends_on:
      - spark-minimal
    deploy:
      resources:
        limits:
          memory: 1g
        reservations:
          memory: 512m
    networks:
      - spark-network

networks:
  spark-network:
    driver: bridge
