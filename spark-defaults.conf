# Spark Configuration for 6GB RAM Windows 10 System with Iceberg
# Optimized for local development and testing

# Memory Configuration (Conservative for 6GB system)
spark.driver.memory                    1g
spark.executor.memory                  1g
spark.driver.maxResultSize             512m
spark.sql.execution.arrow.maxRecordsPerBatch  2048

# Performance Optimizations
spark.sql.adaptive.enabled             true
spark.sql.adaptive.coalescePartitions.enabled  true
spark.sql.adaptive.coalescePartitions.minPartitionNum  1
spark.sql.adaptive.coalescePartitions.initialPartitionNum  8
spark.sql.adaptive.advisoryPartitionSizeInBytes  64MB
spark.sql.adaptive.skewJoin.enabled    true

# Serialization (Kryo is faster than Java serialization)
spark.serializer                       org.apache.spark.serializer.KryoSerializer
spark.kryo.unsafe                      true
spark.kryo.registrationRequired        false

# JVM and GC Settings for low memory environments
spark.driver.extraJavaOptions          -Xms512m -Xmx1g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+UnlockExperimentalVMOptions -XX:+UseZGC
spark.executor.extraJavaOptions        -Xms512m -Xmx1g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+UnlockExperimentalVMOptions -XX:+UseZGC

# Iceberg Configuration for Local Filesystem
spark.sql.extensions                   org.apache.iceberg.spark.extensions.IcebergSparkSessionExtensions
spark.sql.catalog.spark_catalog        org.apache.iceberg.spark.SparkSessionCatalog
spark.sql.catalog.spark_catalog.type   hive
spark.sql.catalog.local                org.apache.iceberg.spark.SparkCatalog
spark.sql.catalog.local.type           rest
spark.sql.catalog.local.uri            http://rest:8181
spark.sql.catalog.local.warehouse      file:///home/<USER>/warehouse

# Hadoop Configuration for Local Filesystem
spark.hadoop.fs.defaultFS              file:///
spark.sql.warehouse.dir                file:///home/<USER>/warehouse

# Network and Shuffle Configuration
spark.network.timeout                  300s
spark.sql.broadcastTimeout             300
spark.shuffle.compress                 true
spark.shuffle.spill.compress           true
spark.io.compression.codec             snappy

# Partition and File Configuration
spark.sql.files.maxPartitionBytes      128MB
spark.sql.files.openCostInBytes        4MB
spark.sql.parquet.compression.codec    snappy
spark.sql.parquet.enableVectorizedReader  true

# Dynamic Allocation (Disabled for single-node setup)
spark.dynamicAllocation.enabled        false
spark.executor.instances               1
spark.executor.cores                   2

# UI and History
spark.ui.enabled                       true
spark.ui.port                          4040
spark.history.ui.port                  18080

# Logging
spark.sql.execution.arrow.pyspark.enabled  true
spark.sql.repl.eagerEval.enabled       true
spark.sql.repl.eagerEval.maxNumRows    20
