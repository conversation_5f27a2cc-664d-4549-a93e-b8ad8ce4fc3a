@echo off
echo ========================================
echo  Spark-Iceberg Local Setup for Windows
echo  Robust Deployment with Network Retry
echo ========================================

echo.
echo 🚀 Starting robust deployment...

REM Check if Docker is running
echo 🐳 Checking Docker status...
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not running! Please start Docker Desktop first.
    pause
    exit /b 1
)
echo ✅ Docker is running

REM Stop any existing containers
echo.
echo 🛑 Stopping existing containers...
docker-compose down

REM Create necessary directories
echo.
echo 📁 Creating directories...
if not exist "warehouse" mkdir warehouse
if not exist "notebooks" mkdir notebooks
if not exist "data" mkdir data
if not exist "src" mkdir src
echo ✅ Directories created

REM Copy initialization script to src
echo.
echo 📋 Setting up initialization script...
copy spark_iceberg_init.py src\ >nul 2>&1
echo ✅ Initialization script ready

REM Pull images individually with retry logic
echo.
echo 📥 Downloading Docker images (this may take several minutes)...
echo    Total download size: ~2GB

echo.
echo 🔄 Pulling Spark-Iceberg image (largest, ~1.5GB)...
:retry_spark
docker pull tabulario/spark-iceberg:1.5.0
if %errorlevel% neq 0 (
    echo ⚠️  Network timeout pulling Spark image. Retrying in 10 seconds...
    timeout /t 10 /nobreak >nul
    goto retry_spark
)
echo ✅ Spark-Iceberg image downloaded

echo.
echo 🔄 Pulling REST Catalog image (~250MB)...
:retry_rest
docker pull tabulario/iceberg-rest:1.5.0
if %errorlevel% neq 0 (
    echo ⚠️  Network timeout pulling REST image. Retrying in 10 seconds...
    timeout /t 10 /nobreak >nul
    goto retry_rest
)
echo ✅ REST Catalog image downloaded

echo.
echo 🚀 Starting services...
docker-compose up -d

if %errorlevel% neq 0 (
    echo ❌ Failed to start services. Please check Docker logs.
    echo    Try running: docker-compose logs
    pause
    exit /b 1
)

REM Wait for services to be ready
echo.
echo ⏳ Waiting for services to initialize...
timeout /t 30 /nobreak >nul

REM Check service status
echo.
echo 🔍 Checking service status...
docker-compose ps

REM Test connectivity
echo.
echo 🧪 Testing service connectivity...

REM Test Jupyter (may take a moment to start)
echo    Testing Jupyter Notebook...
for /L %%i in (1,1,6) do (
    curl -s http://localhost:8888 >nul 2>&1
    if !errorlevel! equ 0 (
        echo ✅ Jupyter is accessible at http://localhost:8888
        goto jupyter_ready
    )
    echo    Attempt %%i/6: Waiting for Jupyter to start...
    timeout /t 10 /nobreak >nul
)
echo ⚠️  Jupyter may still be starting. Check http://localhost:8888 in a few minutes.
:jupyter_ready

REM Test REST Catalog
echo    Testing REST Catalog...
curl -s http://localhost:8181/v1/config >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ REST Catalog is accessible at http://localhost:8181
) else (
    echo ⚠️  REST Catalog may still be starting. Check logs if issues persist.
)

echo.
echo ========================================
echo  🎉 Robust Deployment Complete!
echo ========================================
echo.
echo 📊 Access Points:
echo   • Jupyter Notebook: http://localhost:8888
echo   • Spark UI:         http://localhost:4040 (when jobs run)
echo   • Spark Master UI:  http://localhost:8080
echo   • REST Catalog:     http://localhost:8181
echo.
echo 📝 Next Steps:
echo   1. Open http://localhost:8888 in your browser
echo   2. Try the 'local_iceberg_demo.ipynb' notebook first
echo   3. Then explore 'event_data_pyspark.ipynb'
echo.
echo 💾 Data Storage:
echo   • Warehouse: ./warehouse (local filesystem)
echo   • Notebooks: ./notebooks
echo   • Data: ./data
echo.
echo 🔧 Memory Usage (optimized for 6GB RAM):
echo   • Spark Driver: 1GB
echo   • Spark Executor: 1GB
echo   • Total Docker: ~3GB
echo.
echo 🛠️  Troubleshooting:
echo   • If services don't start: docker-compose logs
echo   • If out of memory: close other applications
echo   • If network issues: this script handles retries
echo.
echo Press any key to continue...
pause >nul
