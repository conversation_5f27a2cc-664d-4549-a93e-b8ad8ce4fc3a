@echo off
echo ========================================
echo  Spark-Iceberg Local Setup for Windows
echo  Optimized for 6GB RAM
echo ========================================

echo.
echo 🚀 Starting deployment...

REM Check if Docker is running
echo 🐳 Checking Docker status...
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not running! Please start Docker Desktop first.
    pause
    exit /b 1
)
echo ✅ Docker is running

REM Stop any existing containers
echo.
echo 🛑 Stopping existing containers...
docker-compose down

REM Create necessary directories
echo.
echo 📁 Creating directories...
if not exist "warehouse" mkdir warehouse
if not exist "notebooks" mkdir notebooks
if not exist "data" mkdir data
if not exist "src" mkdir src
echo ✅ Directories created

REM Copy initialization script to src
echo.
echo 📋 Setting up initialization script...
copy spark_iceberg_init.py src\ >nul 2>&1
echo ✅ Initialization script ready

REM Start the services
echo.
echo 🚀 Starting Spark-Iceberg services...
docker-compose up -d

REM Wait for services to be ready
echo.
echo ⏳ Waiting for services to start...
timeout /t 30 /nobreak >nul

REM Check service status
echo.
echo 🔍 Checking service status...
docker-compose ps

echo.
echo ========================================
echo  🎉 Deployment Complete!
echo ========================================
echo.
echo 📊 Access Points:
echo   • Jupyter Notebook: http://localhost:8888
echo   • Spark UI:         http://localhost:4040
echo   • Spark Master UI:  http://localhost:8080
echo   • REST Catalog:     http://localhost:8181
echo.
echo 📝 Next Steps:
echo   1. Open http://localhost:8888 in your browser
echo   2. Try the 'local_iceberg_demo.ipynb' notebook
echo   3. Or use the updated 'event_data_pyspark.ipynb'
echo.
echo 💾 Data Storage:
echo   • Warehouse: ./warehouse (local filesystem)
echo   • Notebooks: ./notebooks
echo   • Data: ./data
echo.
echo 🔧 Memory Configuration:
echo   • Spark Driver: 1GB
echo   • Spark Executor: 1GB
echo   • Total Docker Memory: ~3GB
echo.
echo Press any key to continue...
pause >nul
