from pyspark.sql import SparkSession

def do_user_devices_cumulated(spark, events_df, devices_df, user_devices_cumulated_df):
    query = f"""
   with today as (
    select 
    e.user_id,
    date (e.event_time) as date,
    e.device_id,
    d.browser_type,
    row_number() over(partition by e.user_id, e.device_id, d.browser_type order by e.event_time) as row_num
    from events as e
    left join devices as d
    on e.device_id = d.device_id
    where date(e.event_time) = date('2023-01-02')
    and e.user_id is not null 
    and e.device_id is not null
    ), deduped_today as (
      select 
        *
      from today
      where row_num = 1
    ), yesterday as (
      select * 
      from user_devices_cumulated
      where date = date ('2023-01-01')
    )
    select 
      coalesce(t.user_id, y.user_id) as user_id,
      coalesce(t.device_id, y.device_id) as device_id,
      coalesce(t.browser_type, y.browser_type) as browser_type,
      coalesce(t.date, y.date + 1) as date,
      case 
        when y.device_activity_datelist is null
        then array(t.date)
        when t.date is null
        then y.device_activity_datelist
        else y.device_activity_datelist || array(t.date)
      end as device_activity_datelist
    from deduped_today as t
    full outer join yesterday as y
    on t.user_id = y.user_id
    and t.device_id = y.device_id
    and t.browser_type = y.browser_type;
    """
    events_df.createOrReplaceTempView("events")
    devices_df.createOrReplaceTempView("devices")
    user_devices_cumulated_df.createOrReplaceTempView("user_devices_cumulated")

    return spark.sql(query)

def main():
    ds = '2023-01-01'
    spark = SparkSession.builder \
      .master("local") \
      .appName("user_devices_cumulated") \
      .getOrCreate()
    output_df = do_user_devices_cumulated(spark, spark.table("events"), spark.table("devices"), spark.table("user_devices_cumulated"))
    output_df.write.mode("overwrite").insertInto("user_devices_cumulated")